<?php
require_once('config/database.php');
include 'top_bar_eval_IT.php';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Ajouter un Interlocuteur</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f8f9fa; margin: 40px; }
        .form-container { background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #ddd; padding: 32px; max-width: 500px; margin: auto; }
        label { display: block; margin-top: 16px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; margin-top: 4px; border-radius: 4px; border: 1px solid #ccc; }
        button { margin-top: 24px; padding: 10px 24px; background: #005ea2; color: #fff; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #003d6b; }
    </style>
</head>
<body>
    <div style="margin-bottom:24px;">
        <a href="evaluation_system.php" style="display:inline-block;padding:10px 22px;background:#005ea2;color:#fff;border-radius:5px;text-decoration:none;font-weight:bold;">&larr; Retour à l'accueil</a>
    </div>
    <div class="form-container">
        <h1>Ajouter un Interlocuteur</h1>
        <form method="post" action="">
            <label for="nom">Nom *</label>
            <input type="text" id="nom" name="nom" required>

            <label for="prenom">Prénom *</label>
            <input type="text" id="prenom" name="prenom" required>

            <label for="telephone">Numéro de téléphone *</label>
            <input type="text" id="telephone" name="telephone" required>

            <label for="email">Email *</label>
            <input type="email" id="email" name="email" required>

            <label for="entreprise">Entreprise *</label>
            <input type="text" id="entreprise" name="entreprise" required>

            <label for="poste">Poste</label>
            <input type="text" id="poste" name="poste">

            <label for="departement">Département</label>
            <input type="text" id="departement" name="departement">

            <label for="notes">Notes supplémentaires</label>
            <textarea id="notes" name="notes"></textarea>

            <button type="submit">Ajouter</button>
        </form>
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $nom = $_POST['nom'];
            $prenom = $_POST['prenom'];
            $telephone = $_POST['telephone'];
            $email = $_POST['email'];
            $entreprise = $_POST['entreprise'];
            $poste = $_POST['poste'];
            $departement = $_POST['departement'];
            $notes = $_POST['notes'];
            $sql = "INSERT INTO interlocuteurs (nom, prenom, telephone, email, entreprise, poste, departement, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$nom, $prenom, $telephone, $email, $entreprise, $poste, $departement, $notes]);
            echo '<p style="color:green;">Interlocuteur ajouté avec succès !</p>';
        }
        ?>
    </div>
</body>
</html>
