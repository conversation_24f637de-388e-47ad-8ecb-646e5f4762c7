<?php
require_once __DIR__ . '/config/database.php';
// Simuler un accès IT (à remplacer par votre auth réelle)
$isIT = true;
if (!$isIT) {
    die('Accès réservé aux IT.');
}
$msg = '';
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$guide = null;
if ($id) {
    $stmt = $db->prepare('SELECT * FROM guides WHERE id = ?');
    $stmt->execute([$id]);
    $guide = $stmt->fetch(PDO::FETCH_ASSOC);
}
if (!$guide) {
    die('Guide introuvable.');
}
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $titre = trim($_POST['titre'] ?? '');
    $categorie = trim($_POST['categorie'] ?? '');
    $contenu = trim($_POST['contenu'] ?? '');
    $pdfFileName = $guide['pdf'];
    if (isset($_FILES['pdf']) && $_FILES['pdf']['error'] === UPLOAD_ERR_OK) {
        $ext = strtolower(pathinfo($_FILES['pdf']['name'], PATHINFO_EXTENSION));
        if ($ext === 'pdf') {
            $pdfFileName = uniqid('guide_', true) . '.pdf';
            $dest = __DIR__ . '/pdf_doc/' . $pdfFileName;
            if (move_uploaded_file($_FILES['pdf']['tmp_name'], $dest)) {
                if ($guide['pdf'] && file_exists(__DIR__ . '/pdf_doc/' . $guide['pdf'])) {
                    @unlink(__DIR__ . '/pdf_doc/' . $guide['pdf']);
                }
            } else {
                $msg = "<span style='color:red'>Erreur lors de l'upload du PDF.</span>";
            }
        } else {
            $msg = "<span style='color:red'>Le fichier doit être un PDF.</span>";
        }
    }
    if ($titre && $categorie && $contenu && $msg === '') {
        $stmt = $db->prepare('UPDATE guides SET titre=?, categorie=?, contenu=?, pdf=? WHERE id=?');
        if ($stmt->execute([$titre, $categorie, $contenu, $pdfFileName, $id])) {
            header('Location: doc.php');
            exit;
        } else {
            $msg = "<span style='color:red'>Erreur lors de la modification.</span>";
        }
    } elseif ($msg === '') {
        $msg = "<span style='color:red'>Tous les champs sont obligatoires.";
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Modifier le guide</title>
    <link rel="stylesheet" href="styles.css">
    <style>body{font-family:Arial,sans-serif;background:#f7f7fa;}.admin-container{max-width:600px;margin:40px auto;background:#fff;border-radius:10px;box-shadow:0 2px 8px #0001;padding:32px;}h1{color:#2a3b8f;margin-bottom:24px;}label{font-weight:bold;}input,select,textarea{width:100%;padding:8px;margin-bottom:16px;border-radius:5px;border:1px solid #ccc;}button{background:#2a3b8f;color:#fff;padding:10px 24px;border:none;border-radius:5px;cursor:pointer;}.back-link{display:inline-block;margin-bottom:24px;color:#2a3b8f;text-decoration:underline;}</style>
</head>
<body>
<div class="admin-container">
    <a href="doc.php" class="back-link">&larr; Retour à la documentation</a>
    <h1>Modifier le guide</h1>
    <?php if ($msg) echo '<div style="margin-bottom:16px;">' . $msg . '</div>'; ?>
    <form method="post" enctype="multipart/form-data">
        <label for="titre">Titre du guide</label>
        <input type="text" id="titre" name="titre" required value="<?= htmlspecialchars($guide['titre']) ?>">
        <label for="categorie">Catégorie</label>
        <select id="categorie" name="categorie" required>
            <option value="Réseau" <?= $guide['categorie']==='Réseau'?'selected':'' ?>>Réseau</option>
            <option value="Messagerie" <?= $guide['categorie']==='Messagerie'?'selected':'' ?>>Messagerie</option>
            <option value="Logiciels" <?= $guide['categorie']==='Logiciels'?'selected':'' ?>>Logiciels</option>
            <option value="Matériel" <?= $guide['categorie']==='Matériel'?'selected':'' ?>>Matériel</option>
            <option value="Accès" <?= $guide['categorie']==='Accès'?'selected':'' ?>>Accès</option>
            <option value="Sécurité" <?= $guide['categorie']==='Sécurité'?'selected':'' ?>>Sécurité</option>
            <option value="Autres" <?= $guide['categorie']==='Autres'?'selected':'' ?>>Autres</option>
        </select>
        <label for="contenu">Contenu détaillé</label>
        <textarea id="contenu" name="contenu" required rows="7"><?= htmlspecialchars($guide['contenu']) ?></textarea>
        <label for="pdf">Remplacer le PDF (optionnel)</label>
        <input type="file" id="pdf" name="pdf" accept="application/pdf">
        <?php if ($guide['pdf']): ?>
            <div style="margin-bottom:10px;">PDF actuel : <a href="pdf_doc/<?= urlencode($guide['pdf']) ?>" target="_blank">Voir le PDF</a></div>
        <?php endif; ?>
        <button type="submit">Enregistrer les modifications</button>
    </form>
</div>
</body>
</html>
