<?php
session_start();
// Détection de la langue (GET, SESSION, ou défaut FR)
if (isset($_GET['lang'])) {
    $_SESSION['lang'] = $_GET['lang'];
}
$lang = $_SESSION['lang'] ?? 'fr';
$langFile = __DIR__ . "/lang/{$lang}.php";
if (!file_exists($langFile)) {
    $langFile = __DIR__ . "/lang/fr.php";
}
require_once($langFile);
require_once 'config.php';

// Pour la démonstration, on utilise un ID département fixe
$current_departement_id = 1;

// Récupérer les documents du département
$stmt = $pdo->prepare("
    SELECT d.*, u.nom as utilisateur_nom 
    FROM documents d 
    JOIN utilisateurs u ON d.utilisateur_id = u.id 
    WHERE d.departement_id = ? 
    ORDER BY d.date_upload DESC
");
$stmt->execute([$current_departement_id]);
$documents = $stmt->fetchAll();

// Récupérer les informations du département
$stmt = $pdo->prepare("
    SELECT d.*, e.nom as entreprise_nom 
    FROM departements d 
    JOIN entreprises e ON d.entreprise_id = e.id 
    WHERE d.id = ?
");
$stmt->execute([$current_departement_id]);
$departement = $stmt->fetch();
?>
<!DOCTYPE html>
<html lang="<?= htmlspecialchars($lang) ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $tr['dashboard_manager'] ?? 'Tableau de bord - Manager' ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap5.min.css" rel="stylesheet">
</head>
<body>
    <a href="?lang=fr">Français</a> | <a href="?lang=en">English</a> | <a href="?lang=de">Deutsch</a>
    <?php include 'navbar.php'; ?>

    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Informations du département</h5>
                        <p class="mb-0">
                            <strong>Entreprise:</strong> <?php echo htmlspecialchars($departement['entreprise_nom']); ?><br>
                            <strong>Département:</strong> <?php echo htmlspecialchars($departement['nom']); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Documents du département</h5>
                <div class="table-responsive">
                    <table class="table table-striped" id="documentsTable">
                        <thead>
                            <tr>
                                <th>Commercial</th>
                                <th>Nom du fichier</th>
                                <th>Type</th>
                                <th>Taille</th>
                                <th>Date d'envoi</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($documents as $doc): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($doc['utilisateur_nom']); ?></td>
                                    <td><?php echo htmlspecialchars($doc['nom_du_fichier']); ?></td>
                                    <td><?php echo strtoupper($doc['type_fichier']); ?></td>
                                    <td><?php echo round($doc['taille_fichier'] / 1024 / 1024, 2); ?> MB</td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($doc['date_upload'])); ?></td>
                                    <td>
                                        <a href="download.php?id=<?php echo $doc['id']; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#documentsTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json'
                },
                order: [[4, 'desc']]
            });
        });
    </script>
</body>
</html>
