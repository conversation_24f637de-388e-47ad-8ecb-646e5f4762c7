<?php
require_once __DIR__ . '/config/database.php';
// Simuler un accès IT (à remplacer par votre auth réelle)
$isIT = true;
if (!$isIT) {
    die('Accès réservé aux IT.');
}

$msg = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $titre = trim($_POST['titre'] ?? '');
    $categorie = trim($_POST['categorie'] ?? '');
    $contenu = trim($_POST['contenu'] ?? '');
    $fileName = null;
    $allowedExts = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
    $allowedMimeTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    ];
    if (isset($_FILES['fichier']) && $_FILES['fichier']['error'] === UPLOAD_ERR_OK) {
        $ext = strtolower(pathinfo($_FILES['fichier']['name'], PATHINFO_EXTENSION));
        $mime = $_FILES['fichier']['type'];
        if (in_array($ext, $allowedExts) && in_array($mime, $allowedMimeTypes)) {
            $fileName = uniqid('guide_', true) . '.' . $ext;
            $dest = __DIR__ . '/pdf_doc/' . $fileName;
            if (!move_uploaded_file($_FILES['fichier']['tmp_name'], $dest)) {
                $msg = "<span style='color:red'>Erreur lors de l'upload du fichier.</span>";
                $fileName = null;
            }
        } else {
            $msg = "<span style='color:red'>Type de fichier non autorisé. (PDF, Word, Excel, PowerPoint)</span>";
        }
    }
    if ($titre && $categorie && $contenu && ($msg === '')) {
        $stmt = $db->prepare('INSERT INTO guides (titre, categorie, contenu, pdf) VALUES (?, ?, ?, ?)');
        if ($stmt->execute([$titre, $categorie, $contenu, $fileName])) {
            $msg = "<span style='color:green'>Guide enregistré avec succès !</span> <a href='doc.php'>Voir la documentation</a>";
        } else {
            $msg = "<span style='color:red'>Erreur lors de l'enregistrement.</span>";
        }
    } elseif ($msg === '') {
        $msg = "<span style='color:red'>Tous les champs sont obligatoires.</span>";
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ajouter un guide détaillé</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body { font-family: Arial, sans-serif; background: #f7f7fa; margin: 0; }
        .admin-container { max-width: 600px; margin: 40px auto; background: #fff; border-radius: 10px; box-shadow: 0 2px 8px #0001; padding: 32px; }
        h1 { color: #2a3b8f; margin-bottom: 24px; }
        label { font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; margin-bottom: 16px; border-radius: 5px; border: 1px solid #ccc; }
        button { background: #2a3b8f; color: #fff; padding: 10px 24px; border: none; border-radius: 5px; cursor: pointer; }
        .back-link { display: inline-block; margin-bottom: 24px; color: #2a3b8f; text-decoration: underline; }
    </style>
</head>
<body>
<div class="admin-container">
    <a href="doc.php" class="back-link">&larr; Retour à la documentation</a>
    <h1>Ajouter un guide détaillé</h1>
    <?php if ($msg) echo '<div style="margin-bottom:16px;">' . $msg . '</div>'; ?>
    <form method="post" action="#" enctype="multipart/form-data">
        <label for="titre">Titre du guide</label>
        <input type="text" id="titre" name="titre" required>

        <label for="categorie">Catégorie</label>
        <select id="categorie" name="categorie" required>
            <option value="Réseau">Réseau</option>
            <option value="Messagerie">Messagerie</option>
            <option value="Logiciels">Logiciels</option>
            <option value="Matériel">Matériel</option>
            <option value="Accès">Accès</option>
            <option value="Sécurité">Sécurité</option>
            <option value="Autres">Autres</option>
        </select>

        <label for="contenu">Contenu détaillé</label>
        <textarea id="contenu" name="contenu" required rows="7"></textarea>

        <label for="fichier">Ajouter un fichier (PDF, Word, Excel, PowerPoint) (optionnel)</label>
        <input type="file" id="fichier" name="fichier" accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx">

        <button type="submit">Enregistrer le guide</button>
    </form>
   </div>
</body>
</html>
