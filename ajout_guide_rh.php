<?php
require_once __DIR__ . '/config/database.php';
session_start();
function isAdmin() {
    return (isset($_SESSION['is_global_admin']) && $_SESSION['is_global_admin']) || (isset($_SESSION['sAMAccountName']) && strtolower($_SESSION['sAMAccountName']) === 'ccartier');
}
// Contrôle d'accès RH :
$isRH = (
    (isset($_SESSION['role']) && $_SESSION['role'] === 'RH') ||
    (isset($_SESSION['role']) && $_SESSION['role'] === 'IT') ||
    isAdmin()
);
if (!$isRH) {
    header('Location: docs_services.php');
    exit();
}
$msg = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $titre = trim($_POST['titre'] ?? '');
    $categorie = trim($_POST['categorie'] ?? '');
    $contenu = trim($_POST['contenu'] ?? '');
    $pdfFileName = null;
    if (isset($_FILES['pdf']) && $_FILES['pdf']['error'] === UPLOAD_ERR_OK) {
        $ext = strtolower(pathinfo($_FILES['pdf']['name'], PATHINFO_EXTENSION));
        if ($ext === 'pdf') {
            $pdfFileName = uniqid('guide_rh_', true) . '.pdf';
            $dest = __DIR__ . '/pdf_doc/' . $pdfFileName;
            if (!move_uploaded_file($_FILES['pdf']['tmp_name'], $dest)) {
                $msg = "<span style='color:red'>Erreur lors de l'upload du PDF.</span>";
                $pdfFileName = null;
            }
        } else {
            $msg = "<span style='color:red'>Le fichier doit être un PDF.</span>";
        }
    }
    if ($titre && $categorie && $contenu && ($msg === '')) {
        $stmt = $db->prepare('INSERT INTO guides_rh (titre, categorie, contenu, pdf, created_at) VALUES (?, ?, ?, ?, NOW())');
        if ($stmt->execute([$titre, $categorie, $contenu, $pdfFileName])) {
            $msg = "<span style='color:green'>Guide RH enregistré avec succès !</span> <a href='doc_rh.php'>Voir la documentation</a>";
        } else {
            $msg = "<span style='color:red'>Erreur lors de l'enregistrement.</span>";
        }
    } elseif ($msg === '') {
        $msg = "<span style='color:red'>Tous les champs sont obligatoires.</span>";
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ajouter un guide RH</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body { font-family: Arial, sans-serif; background: #f7f7fa; margin: 0; }
        .admin-container { max-width: 600px; margin: 40px auto; background: #fff; border-radius: 10px; box-shadow: 0 2px 8px #0001; padding: 32px; }
        h1 { color: #2a3b8f; margin-bottom: 24px; }
        label { font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; margin-bottom: 16px; border-radius: 5px; border: 1px solid #ccc; }
        button { background: #2a3b8f; color: #fff; padding: 10px 24px; border: none; border-radius: 5px; cursor: pointer; }
        .back-link { display: inline-block; margin-bottom: 24px; color: #2a3b8f; text-decoration: underline; }
    </style>
</head>
<body>
<div class="admin-container">
    <a href="doc_rh.php" class="back-link">&larr; Retour à la documentation</a>
    <h1>Ajouter un guide RH</h1>
    <?php if ($msg) echo '<div style="margin-bottom:16px;">' . $msg . '</div>'; ?>
    <form method="post" action="#" enctype="multipart/form-data">
        <label for="titre">Titre du guide</label>
        <input type="text" id="titre" name="titre" required>

        <label for="categorie">Catégorie</label>
        <select id="categorie" name="categorie" required>
            <option value="Congés">Congés</option>
            <option value="Contrats">Contrats</option>
            <option value="Paie">Paie</option>
            <option value="Autres">Autres</option>
        </select>

        <label for="contenu">Contenu détaillé</label>
        <textarea id="contenu" name="contenu" required rows="7"></textarea>

        <label for="pdf">Ajouter un PDF (optionnel)</label>
        <input type="file" id="pdf" name="pdf" accept="application/pdf">

        <button type="submit">Enregistrer le guide</button>
    </form>
</div>
</body>
</html>
