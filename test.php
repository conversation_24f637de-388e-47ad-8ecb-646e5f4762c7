<?php
session_start();
// Détection de la langue (GET, SESSION, ou défaut FR)
if (isset($_GET['lang'])) {
    $_SESSION['lang'] = $_GET['lang'];
}
$lang = $_SESSION['lang'] ?? 'fr';
$langFile = __DIR__ . "/lang/{$lang}.php";
if (!file_exists($langFile)) {
    $langFile = __DIR__ . "/lang/fr.php";
}
require_once($langFile);

// Vérifie si l'utilisateur est connecté
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in']) {
    header('Location: login.php');
    exit();
}

// Paramètres LDAP (identiques à login.php)
$ldap_host = 'FRVIVM001.schlueter.de';
$ldap_port = 389;
$ldap_domain = 'ISERLOHN';
$ldap_base_dn = 'dc=schlueter,dc=de';

// Récupère le login et le mot de passe depuis la session
$samaccount = $_SESSION['sAMAccountName'] ?? null;
$password = $_SESSION['user_password'] ?? null;
if (!$samaccount || !$password) {
    echo "Impossible de retrouver le login ou le mot de passe de l'utilisateur. Veuillez vous reconnecter.";
    exit();
}

$ldap_conn = ldap_connect($ldap_host, $ldap_port);
ldap_set_option($ldap_conn, LDAP_OPT_PROTOCOL_VERSION, 3);
ldap_set_option($ldap_conn, LDAP_OPT_REFERRALS, 0);
$ldap_user = $ldap_domain . "\\" . $samaccount;

if (!@ldap_bind($ldap_conn, $ldap_user, $password)) {
    echo "Impossible de se connecter à l'annuaire LDAP avec vos identifiants.";
    exit();
}

$attributes = [
    'displayName', 'mail', 'givenName', 'sn', 'sAMAccountName', 'userPrincipalName', 'title', 'telephoneNumber', 'department', 'company', 'memberOf', 'whenCreated', 'whenChanged', 'distinguishedName', 'physicalDeliveryOfficeName', 'manager', 'description'
];
$filter = "(sAMAccountName=" . ldap_escape($samaccount, '', LDAP_ESCAPE_FILTER) . ")";
$result = ldap_search($ldap_conn, $ldap_base_dn, $filter, $attributes);
$entries = ldap_get_entries($ldap_conn, $result);

?>
<!DOCTYPE html>
<html lang="<?= htmlspecialchars($lang) ?>">
<head>
    <meta charset="UTF-8">
    <title><?= $tr['test_title'] ?? 'Test' ?></title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body { font-family: Arial, sans-serif; background: #f4f4f4; }
        .container { max-width: 700px; margin: 40px auto; background: #fff; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.08); padding: 30px; }
        h1 { text-align: center; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { padding: 10px; border-bottom: 1px solid #eee; text-align: left; }
        th { background: #f57c00; color: #fff; }
        tr:last-child td { border-bottom: none; }
    </style>
</head>
<body>
    <a href="?lang=fr">Français</a> | <a href="?lang=en">English</a> | <a href="?lang=de">Deutsch</a>
<div class="container">
    <h1>Données Active Directory de l'utilisateur</h1>
    <?php if ($entries['count'] > 0): ?>
        <table>
            <thead><tr><th>Attribut</th><th>Valeur</th></tr></thead>
            <tbody>
            <?php foreach ($attributes as $attr): ?>
                <tr>
                    <td><?= htmlspecialchars($attr) ?></td>
                    <td>
                        <?php
                        if (isset($entries[0][strtolower($attr)])) {
                            $val = $entries[0][strtolower($attr)];
                            if (is_array($val)) {
                                // Plusieurs valeurs (ex: memberOf)
                                $vals = [];
                                foreach ($val as $k => $v) {
                                    if ($k === 'count') continue;
                                    $vals[] = $v;
                                }
                                echo htmlspecialchars(implode(', ', $vals));
                            } else {
                                echo htmlspecialchars($val);
                            }
                        } else {
                            echo '<em>Non disponible</em>';
                        }
                        ?>
                    </td>
                </tr>
            <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <p style="color:red;">Aucune donnée trouvée pour cet utilisateur dans l'AD.</p>
    <?php endif; ?>
</div>
</body>
</html>
