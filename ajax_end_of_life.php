<?php
require_once 'config/database.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    if ($action === 'add') {
        $nom = $_POST['nom'] ?? '';
        $ancien_poste = $_POST['ancien_poste'] ?? '';
        $nouveau_poste = $_POST['nouveau_poste'] ?? '';
        $date = $_POST['date'] ?? '';
        $lifecycle = $_POST['lifecycle'] ?? '';
        $responsable = $_POST['responsable'] ?? '';
        $type = $_POST['type'] ?? '';
        if ($nom && $ancien_poste && $date && $lifecycle && $responsable && $type) {
            $sql = "INSERT INTO changements_poste (nom_utilisateur, ancien_poste, nouveau_poste, date_changement, lifecycle, responsable, type) VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$nom, $ancien_poste, $nouveau_poste, $date, $lifecycle, $responsable, $type]);
            echo json_encode(['success' => true]);
            exit;
        } else {
            echo json_encode(['success' => false, 'error' => 'Champs manquants']);
            exit;
        }
    } elseif ($action === 'delete') {
        $id = $_POST['id'] ?? '';
        if ($id) {
            $sql = "DELETE FROM changements_poste WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$id]);
            echo json_encode(['success' => true]);
            exit;
        } else {
            echo json_encode(['success' => false, 'error' => 'ID manquant']);
            exit;
        }
    }
} elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Rafraîchir les listes
    $type = $_GET['type'] ?? '';
    if (in_array($type, ['pc_installes', 'pc_prevus', 'pc_a_prevoir'])) {
        $sql = "SELECT * FROM changements_poste WHERE type = ? ORDER BY date_changement DESC";
        $stmt = $db->prepare($sql);
        $stmt->execute([$type]);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode(['success' => true, 'data' => $data]);
        exit;
    }
}
echo json_encode(['success' => false, 'error' => 'Requête invalide']);
