<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Ajouter un Prestataire</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f8f9fa; margin: 40px; }
        .form-container { background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #ddd; padding: 32px; max-width: 500px; margin: auto; }
        label { display: block; margin-top: 16px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; margin-top: 4px; border-radius: 4px; border: 1px solid #ccc; }
        button { margin-top: 24px; padding: 10px 24px; background: #005ea2; color: #fff; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #003d6b; }
    </style>
</head>
<body>
    <?php include 'top_bar_eval_IT.php'; ?>
    <div style="margin-bottom:24px;">
        <a href="evaluation_system.php" style="display:inline-block;padding:10px 22px;background:#005ea2;color:#fff;border-radius:5px;text-decoration:none;font-weight:bold;">&larr; Retour à l'accueil</a>
    </div>
    <div class="form-container">
        <h1>Ajouter un Prestataire</h1>
        <form method="post" action="">
            <label for="nom">Nom *</label>
            <input type="text" id="nom" name="nom" required>

            <label for="email">Email *</label>
            <input type="email" id="email" name="email" required>

            <label for="telephone">Numéro de téléphone *</label>
            <input type="text" id="telephone" name="telephone" required>

            <label for="adresse">Adresse</label>
            <input type="text" id="adresse" name="adresse">

            <label for="siteweb">Site web</label>
            <input type="text" id="siteweb" name="siteweb">

            <label for="secteur">Secteur d'activité</label>
            <input type="text" id="secteur" name="secteur">

            <button type="submit">Ajouter</button>
        </form>
        <?php
        require_once('config/database.php');
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $nom = $_POST['nom'];
            $email = $_POST['email'];
            $telephone = $_POST['telephone'];
            $adresse = $_POST['adresse'];
            $siteweb = $_POST['siteweb'];
            $secteur = $_POST['secteur'];
            $sql = "INSERT INTO prestataires (nom, email, telephone, adresse, siteweb, secteur) VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$nom, $email, $telephone, $adresse, $siteweb, $secteur]);
            echo '<p style="color:green;">Prestataire ajouté avec succès !</p>';
        }
        ?>
    </div>
</body>
</html>
