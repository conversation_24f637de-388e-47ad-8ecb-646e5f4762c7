<?php
require_once __DIR__ . '/config/database.php';
header('Content-Type: application/json');
if (!isset($_POST['id']) || !isset($_POST['vote'])) {
    echo json_encode(['success' => false, 'msg' => 'Paramètres manquants']);
    exit;
}
$id = (int)$_POST['id'];
$vote = $_POST['vote'] === 'up' ? 'votes_up' : 'votes_down';
$cookieName = 'guide_voted_' . $id;
if (isset($_COOKIE[$cookieName])) {
    echo json_encode(['success' => false, 'msg' => 'Vous avez déjà voté pour ce guide.']);
    exit;
}
try {
    $db->exec("UPDATE guides SET $vote = $vote + 1 WHERE id = $id");
    $stmt = $db->query("SELECT votes_up, votes_down FROM guides WHERE id = $id");
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    setcookie($cookieName, '1', time() + 365*24*3600, '/');
    echo json_encode(['success' => true, 'votes_up' => $row['votes_up'], 'votes_down' => $row['votes_down']]);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'msg' => $e->getMessage()]);
}
