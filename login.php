<?php
session_start();
// Détection de la langue (GET, SESSION, ou défaut FR)
if (isset($_GET['lang'])) {
    $_SESSION['lang'] = $_GET['lang'];
}
$lang = $_SESSION['lang'] ?? 'fr';
$langFile = __DIR__ . "/lang/{$lang}.php";
if (!file_exists($langFile)) {
    $langFile = __DIR__ . "/lang/fr.php";
}
require_once($langFile);

// Déconnexion172.20.221.16
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: login.php');
    exit();
}

// Redirige si déjà connecté
if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true) {
    header('Location: dashboard.php');
    exit();
}

// Paramètres LDAP
$ldap_host = 'FRVIVM001.schlueter.de'; // Nouveau nom du contrôleur de domaine
$ldap_port = 389;
$ldap_domain = 'ISERLOHN'; // Domaine corrigé selon whoami
$ldap_base_dn = 'dc=schlueter,dc=de'; // DN de base élargi pour la recherche

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $ldap_conn = ldap_connect($ldap_host, $ldap_port);
    ldap_set_option($ldap_conn, LDAP_OPT_PROTOCOL_VERSION, 3);
    ldap_set_option($ldap_conn, LDAP_OPT_REFERRALS, 0);
    $ldap_user = $ldap_domain . "\\" . $username;
    if ($ldap_conn && @ldap_bind($ldap_conn, $ldap_user, $password)) {
        // Recherche l'utilisateur pour obtenir displayName
        $filter = "(sAMAccountName=" . ldap_escape($username, '', LDAP_ESCAPE_FILTER) . ")";
        $attributes = ['displayName', 'department'];
        $result = ldap_search($ldap_conn, $ldap_base_dn, $filter, $attributes);
        $entries = ldap_get_entries($ldap_conn, $result);
        if ($entries['count'] > 0 && !empty($entries[0]['displayname'][0])) {
            $_SESSION['logged_in'] = true;
            $_SESSION['displayName'] = $entries[0]['displayname'][0];
            $_SESSION['sAMAccountName'] = $username;
            $_SESSION['user_password'] = $password;
            // Gestion du rôle IT selon le département ou l'identifiant
            $isIT = false;
            // Vérification par département AD
            if (!empty($entries[0]['department'][0]) && stripos($entries[0]['department'][0], 'IT') !== false) {
                $isIT = true;
            }
            // OU vérification par identifiant (ajoute d'autres logins si besoin)
            $itAdmins = ['ccartier'];
            if (in_array(strtolower($username), array_map('strtolower', $itAdmins))) {
                $isIT = true;
            }
            if ($isIT) {
                $_SESSION['role'] = 'IT';
            } else {
                unset($_SESSION['role']);
            }
            ldap_unbind($ldap_conn);
            header('Location: index.php');
            exit();
        } else {
            $error = "Utilisateur introuvable dans l'annuaire LDAP.";
        }
        ldap_unbind($ldap_conn);
    } else {
        $ldap_error = ldap_error($ldap_conn);
        $error = "Login ou mot de passe incorrect. Erreur LDAP : $ldap_error";
    }
}

// Après connexion LDAP réussie, vérifier si le token Microsoft Graph est déjà en session
if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true && !isset($_SESSION['access_token'])) {
    // Paramètres Azure AD
    $clientId = 'VOTRE_CLIENT_ID';
    $clientSecret = 'VOTRE_CLIENT_SECRET';
    $tenantId = 'VOTRE_TENANT_ID';
    $redirectUri = 'http://localhost/login.php'; // ou l'URL de votre serveur

    // 1. Si l'utilisateur revient avec un code d'autorisation
    if (isset($_GET['code'])) {
        $code = $_GET['code'];
        $tokenUrl = "https://login.microsoftonline.com/$tenantId/oauth2/v2.0/token";
        $postFields = [
            'client_id' => $clientId,
            'scope' => 'https://graph.microsoft.com/.default offline_access',
            'code' => $code,
            'redirect_uri' => $redirectUri,
            'grant_type' => 'authorization_code',
            'client_secret' => $clientSecret,
        ];
        $ch = curl_init($tokenUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postFields));
        $response = curl_exec($ch);
        curl_close($ch);
        $tokenData = json_decode($response, true);
        if (isset($tokenData['access_token'])) {
            $_SESSION['access_token'] = $tokenData['access_token'];
            // Redirige vers la page d'accueil ou dashboard
            header('Location: index.php');
            exit();
        } else {
            echo 'Erreur lors de la récupération du token Microsoft Graph.';
            exit();
        }
    }
    // 2. Sinon, rediriger vers Azure AD pour obtenir le code d'autorisation
    else {
        $authUrl = "https://login.microsoftonline.com/$tenantId/oauth2/v2.0/authorize?" . http_build_query([
            'client_id' => $clientId,
            'response_type' => 'code',
            'redirect_uri' => $redirectUri,
            'response_mode' => 'query',
            'scope' => 'https://graph.microsoft.com/.default offline_access',
            'state' => '12345',
        ]);
        header('Location: ' . $authUrl);
        exit();
    }
}
?>
<!DOCTYPE html>
<html lang="<?= htmlspecialchars($lang) ?>">
<head>
    <meta charset="UTF-8">
    <title><?= $tr['login_title'] ?? 'Connexion' ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="styles.css">
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden; /* Empêche le scroll */
        }
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            min-height: 100vh;
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
        }
        .login-container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        .login-container h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        .login-container label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .login-container input {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .login-container button {
            width: 100%;
            padding: 10px;
            background-color: #f57c00;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 1em;
            cursor: pointer;
        }
        .login-container button:hover {
            background-color: #e67e22;
        }
        .error {
            color: red;
            font-size: 0.9em;
            margin-bottom: 15px;
            text-align: center;
        }
        .logout-btn {
            margin-top: 20px;
            background: #888;
        }
    </style>
</head>
<body>
    
<div class="login-container">
    <h1><?= $tr['login_title'] ?? 'Connexion' ?></h1>
    <?php if (isset($error)): ?>
        <p class="error"><?= $error ?></p>
    <?php endif; ?>
    <form method="POST" action="login.php">
        <label for="username"><?= $tr['username'] ?? 'Nom d\'utilisateur' ?> :</label>
        <input type="text" id="username" name="username" required autofocus>
        <label for="password"><?= $tr['password'] ?? 'Mot de passe' ?> :</label>
        <input type="password" id="password" name="password" required>
        <button type="submit"><?= $tr['login'] ?? 'Se connecter' ?></button>
    </form>
    <?php if (isset($_SESSION['logged_in']) && $_SESSION['logged_in']): ?>
        <form method="GET" action="login.php">
            <button type="submit" name="logout" value="1" class="logout-btn"><?= $tr['logout'] ?? 'Déconnexion' ?></button>
        </form>
    <?php endif; ?>
</div>
</body>
</html>
