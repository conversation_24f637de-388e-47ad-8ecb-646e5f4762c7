<?php
// Gestion de la langue et fonction de traduction LibreTranslate
if (session_status() === PHP_SESSION_NONE) session_start();
if (isset($_GET['lang'])) {
    $_SESSION['lang'] = $_GET['lang'];
}
$lang = $_SESSION['lang'] ?? 'fr';

function libretranslate($text, $target) {
    if ($target === 'fr') return $text;
    $data = [
        'q' => $text,
        'source' => 'fr',
        'target' => $target,
        'format' => 'text'
    ];
    $ch = curl_init('http://localhost:5000/translate');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    $result = json_decode($response, true);
    if ($http_code !== 200 || !$result || !isset($result['translatedText'])) {
        return '[Translation error: ' . ($curl_error ?: 'API unreachable or quota exceeded') . "] HTTP $http_code | Response: " . htmlspecialchars($response) . ' | Texte: ' . $text;
    }
    return $result['translatedText'];
}

$lang_map = ['fr' => 'fr', 'en' => 'en', 'de' => 'de'];
$lt_lang = $lang_map[$lang] ?? 'fr';
