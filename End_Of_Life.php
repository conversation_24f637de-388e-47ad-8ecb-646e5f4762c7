<?php 
include 'top-bar-informatique.php';
require_once 'config/database.php';

function addChangement($nom, $ancien_poste, $nouveau_poste, $date, $lifecycle, $responsable, $type) {
    global $db;
    $sql = "INSERT INTO changements_poste (nom_utilisateur, ancien_poste, nouveau_poste, date_changement, 
            lifecycle, responsable, type) VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = $db->prepare($sql);
    $stmt->execute([$nom, $ancien_poste, $nouveau_poste, $date, $lifecycle, $responsable, $type]);
}

function deleteChangement($id) {
    global $db;
    $sql = "DELETE FROM changements_poste WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$id]);
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['submit'])) {
        addChangement(
            $_POST['nom'],
            $_POST['ancien_poste'],
            $_POST['nouveau_poste'],
            $_POST['date'],
            $_POST['lifecycle'],
            $_POST['responsable'],
            $_POST['type']
        );
    } elseif (isset($_POST['delete'])) {
        deleteChangement($_POST['id']);
    }
}

// Récupérer les données pour chaque type
$conn = $db;

$sql_installes = "SELECT * FROM changements_poste WHERE type = 'pc_installes' ORDER BY date_changement DESC";
$stmt_installes = $conn->query($sql_installes);
$pc_installes = $stmt_installes->fetchAll(PDO::FETCH_ASSOC);

$sql_prevus = "SELECT * FROM changements_poste WHERE type = 'pc_prevus' ORDER BY date_changement DESC";
$stmt_prevus = $conn->query($sql_prevus);
$pc_prevus = $stmt_prevus->fetchAll(PDO::FETCH_ASSOC);

$sql_a_prevoir = "SELECT * FROM changements_poste WHERE type = 'pc_a_prevoir' ORDER BY date_changement DESC";
$stmt_a_prevoir = $conn->query($sql_a_prevoir);
$pc_a_prevoir = $stmt_a_prevoir->fetchAll(PDO::FETCH_ASSOC);

$sql_all = "SELECT * FROM changements_poste ORDER BY date_changement DESC";
$stmt_all = $conn->query($sql_all);
$all_pc = $stmt_all->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>End of Life - Gestion des Postes</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
    $(function() {
        // Soumission AJAX du formulaire d'ajout
        $(document).on('submit', '.poste-form', function(e) {
            e.preventDefault();
            var form = $(this);
            var data = form.serialize() + '&action=add';
            $.post('ajax_end_of_life.php', data, function(resp) {
                if (resp.success) {
                    refreshTables();
                    form[0].reset();
                } else {
                    alert(resp.error || 'Erreur lors de l\'ajout');
                }
            }, 'json');
        });

        // Suppression AJAX avec confirmation personnalisée
        $(document).on('click', '.btn-danger[data-id]', function(e) {
            e.preventDefault();
            var btn = $(this);
            // Supprimer toute popup existante
            $('.delete-confirm-pop').remove();
            // Positionner la popup près du bouton
            var offset = btn.offset();
            var pop = $('<div class="delete-confirm-pop">'
                + '<div>Confirmer la suppression ?</div>'
                + '<button class="btn-confirm">Oui</button>'
                + '<button class="btn-cancel">Non</button>'
                + '</div>');
            $('body').append(pop);
            pop.css({
                top: offset.top + btn.outerHeight() + 4,
                left: offset.left - 30
            });
            // Annuler
            pop.find('.btn-cancel').on('click', function() {
                pop.remove();
            });
            // Confirmer
            pop.find('.btn-confirm').on('click', function() {
                var id = btn.data('id');
                $.post('ajax_end_of_life.php', {action: 'delete', id: id}, function(resp) {
                    pop.remove();
                    if (resp.success) {
                        refreshTables();
                    } else {
                        alert(resp.error || 'Erreur lors de la suppression');
                    }
                }, 'json');
            });
        });

        // Rafraîchit toutes les tables
        function refreshTables() {
            // Rafraîchir chaque type
            ['pc_installes','pc_prevus','pc_a_prevoir'].forEach(function(type) {
                $.get('ajax_end_of_life.php', {type: type}, function(resp) {
                    if (resp.success) {
                        var tbody = $('#tbody_' + type);
                        tbody.empty();
                        resp.data.forEach(function(changement) {
                            tbody.append(
                                '<tr>' +
                                '<td>' + escapeHtml(changement.nom_utilisateur) + '</td>' +
                                '<td>' + escapeHtml(changement.ancien_poste) + '</td>' +
                                '<td>' + escapeHtml(changement.nouveau_poste) + '</td>' +
                                '<td>' + escapeHtml(changement.date_changement) + '</td>' +
                                '<td>' + escapeHtml(changement.lifecycle) + '</td>' +
                                '<td>' + escapeHtml(changement.responsable) + '</td>' +
                                '<td><button class="btn-danger" data-id="' + changement.id + '" title="Supprimer"><span style="font-size:1.1em;">🗑️</span> Supprimer</button></td>' +
                                '</tr>'
                            );
                        });
                    }
                }, 'json');
            });
            // Rafraîchir le tableau global
            var allData = [];
            var types = [
                {type: 'pc_installes', label: 'PC Installés'},
                {type: 'pc_prevus', label: 'PC Prévus'},
                {type: 'pc_a_prevoir', label: 'PC à Prévoir'}
            ];
            var loaded = 0;
            types.forEach(function(t) {
                $.get('ajax_end_of_life.php', {type: t.type}, function(resp) {
                    if (resp.success) {
                        resp.data.forEach(function(changement) {
                            changement._type_label = t.label;
                            changement._type = t.type;
                            allData.push(changement);
                        });
                    }
                    loaded++;
                    if (loaded === types.length) {
                        var tbody = $('#tbody_all');
                        tbody.empty();
                        allData.sort(function(a, b) {
                            return b.date_changement.localeCompare(a.date_changement);
                        });
                        allData.forEach(function(changement) {
                            tbody.append(
                                '<tr>' +
                                '<td>' + escapeHtml(changement._type_label) + '</td>' +
                                '<td>' + escapeHtml(changement.nom_utilisateur) + '</td>' +
                                '<td>' + escapeHtml(changement.ancien_poste) + '</td>' +
                                '<td>' + escapeHtml(changement.nouveau_poste) + '</td>' +
                                '<td>' + escapeHtml(changement.date_changement) + '</td>' +
                                '<td>' + escapeHtml(changement.lifecycle) + '</td>' +
                                '<td>' + escapeHtml(changement.responsable) + '</td>' +
                                '<td><button class="btn-danger" data-id="' + changement.id + '" title="Supprimer"><span style="font-size:1.1em;">🗑️</span> Supprimer</button></td>' +
                                '</tr>'
                            );
                        });
                    }
                }, 'json');
            });
        }
        // Pour éviter les injections XSS
        function escapeHtml(text) {
            return $('<div>').text(text).html();
        }
        // Initialisation
        refreshTables();

        // Changement de table à afficher
        $('#tableSelect').on('change', function() {
            var selectedType = $(this).val();
            $('.table-container > div').hide();
            $('#table_' + selectedType).show();
        });
        // Barre de recherche dynamique
        $('#searchInput').on('input', function() {
            var search = $(this).val().toLowerCase();
            var tableId = $('#tableSelect').val();
            var tbodyId = tableId === 'all' ? 'tbody_all' : 'tbody_' + tableId;
            $('#' + tbodyId + ' tr').each(function() {
                var rowText = $(this).text().toLowerCase();
                $(this).toggle(rowText.indexOf(search) !== -1);
            });
        });
    });
    </script>
    <style>
    .btn-danger {
        background: #e74c3c;
        color: #fff;
        border: none;
        border-radius: 4px;
        padding: 6px 14px;
        font-size: 1em;
        cursor: pointer;
        transition: background 0.2s;
        display: inline-flex;
        align-items: center;
        gap: 6px;
    }
    .btn-danger:hover {
        background: #c0392b;
    }
    .delete-confirm-pop {
        position: absolute;
        background: #fff;
        border: 1px solid #e74c3c;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.12);
        padding: 14px 18px;
        z-index: 1000;
        min-width: 200px;
        text-align: center;
        font-size: 1em;
        color: #333;
        animation: fadeIn 0.2s;
    }
    .delete-confirm-pop button {
        margin: 8px 6px 0 6px;
        padding: 5px 16px;
        border-radius: 4px;
        border: none;
        font-size: 1em;
        cursor: pointer;
    }
    .delete-confirm-pop .btn-cancel {
        background: #eee;
        color: #333;
    }
    .delete-confirm-pop .btn-confirm {
        background: #e74c3c;
        color: #fff;
    }
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    </style>
</head>
<body>
<div class="container">
    <h1 class="page-title">Gestion des Postes - End of Life</h1>

    <!-- Form Section -->
    <div class="form-section">
        <div class="form-card enhanced-form-card">
            <h2><span style="vertical-align:middle;">&#128187;</span> Ajouter un Poste</h2>
            <form method="POST" action="End_Of_Life.php" class="poste-form">
                <div class="form-row">
                    <label for="type">Catégorie :</label>
                    <select id="type" name="type" required>
                        <option value="" disabled selected>Choisir une catégorie</option>
                        <option value="pc_installes">PC Installés</option>
                        <option value="pc_prevus">PC Prévus</option>
                        <option value="pc_a_prevoir">PC à Prévoir</option>
                    </select>
                </div>
                <div class="form-row">
                    <label for="nom">Nom utilisateur :</label>
                    <input type="text" id="nom" name="nom" placeholder="Jean Dupont" required>
                </div>
                <div class="form-row">
                    <label for="ancien_poste">Ancien poste :</label>
                    <input type="text" id="ancien_poste" name="ancien_poste" placeholder="FRVIC..." required>
                </div>
                <div class="form-row">
                    <label for="nouveau_poste">Nouveau poste :</label>
                    <input type="text" id="nouveau_poste" name="nouveau_poste" placeholder="FRVIC...">
                </div>
                <div class="form-row">
                    <label for="date">Date de changement :</label>
                    <input type="date" id="date" name="date" required>
                </div>
                <div class="form-row">
                    <label for="lifecycle">Lifecycle :</label>
                    <input type="text" id="lifecycle" name="lifecycle" placeholder="2020-2025" required>
                </div>
                <div class="form-row">
                    <label for="responsable">Fait par :</label>
                    <input type="text" id="responsable" name="responsable" placeholder="IT Admin" required>
                </div>
                <div class="form-row form-row-btn">
                    <button type="submit" name="submit" class="btn-primary enhanced-btn">+ Ajouter le poste</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Filtres Section -->
    <div class="filters-section" style="margin-bottom: 24px; display: flex; gap: 18px; align-items: center;">
        <label for="tableSelect"><strong>Afficher :</strong></label>
        <select id="tableSelect" style="padding: 6px 12px; border-radius: 4px;">
            <option value="all">Tous les PC</option>
            <option value="pc_installes">PC Installés</option>
            <option value="pc_prevus">PC Prévus</option>
            <option value="pc_a_prevoir">PC à Prévoir</option>
        </select>
        <input type="text" id="searchInput" placeholder="Rechercher..." style="flex:1; max-width: 320px; padding: 6px 12px; border-radius: 4px; border: 1px solid #ccc;">
    </div>

    <!-- Table Section -->
    <h2 class="section-title">Liste des Postes</h2>
    <div class="table-container">
        <div id="table_all" style="display:none;">
            <h3>Tous les PC</h3>
            <table>
                <thead>
                    <tr>
                        <th>Catégorie</th>
                        <th>Nom</th>
                        <th>Ancien Poste</th>
                        <th>Nouveau Poste</th>
                        <th>Date de Changement</th>
                        <th>Lifecycle</th>
                        <th>Fait par</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="tbody_all">
                    <!-- Rempli dynamiquement par JS -->
                </tbody>
            </table>
        </div>
        <div id="table_pc_installes">
            <h3>PC Installés</h3>
            <table>
                <thead>
                    <tr>
                        <th>Nom</th>
                        <th>Ancien Poste</th>
                        <th>Nouveau Poste</th>
                        <th>Date de Changement</th>
                        <th>Lifecycle</th>
                        <th>Fait par</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="tbody_pc_installes">
                    <!-- Rempli dynamiquement par JS -->
                </tbody>
            </table>
        </div>
        <div id="table_pc_prevus" style="display:none;">
            <h3>PC Prévus</h3>
            <table>
                <thead>
                    <tr>
                        <th>Nom</th>
                        <th>Ancien Poste</th>
                        <th>Nouveau Poste</th>
                        <th>Date de Changement</th>
                        <th>Lifecycle</th>
                        <th>Fait par</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="tbody_pc_prevus">
                    <!-- Rempli dynamiquement par JS -->
                </tbody>
            </table>
        </div>
        <div id="table_pc_a_prevoir" style="display:none;">
            <h3>PC à Prévoir</h3>
            <table>
                <thead>
                    <tr>
                        <th>Nom</th>
                        <th>Ancien Poste</th>
                        <th>Nouveau Poste</th>
                        <th>Date de Changement</th>
                        <th>Lifecycle</th>
                        <th>Fait par</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="tbody_pc_a_prevoir">
                    <!-- Rempli dynamiquement par JS -->
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php include 'bottom_bar.php'; ?>
</body>
</html>