/* Organigramme modulaire - Style moderne et responsive */
.org-container {
    max-width: 100vw;
    padding: 2rem;
    background: #f8f9fa;
    font-family: 'Segoe UI', <PERSON>l, sans-serif;
}
.org-legend {
    margin-bottom: 1rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}
.legend-block {
    display: inline-block;
    padding: 0.3em 1em;
    border-radius: 1em;
    font-size: 0.95em;
    color: #fff;
    margin-right: 0.5em;
    margin-bottom: 0.3em;
}
.legend-block.rh, .dept-rh { background: #0074d9; }
.legend-block.it, .dept-it { background: #2ecc40; }
.legend-block.commercial, .dept-commercial { background: #ff851b; }
.legend-block.autres, .dept-autres { background: #888; }
.legend-block.cdi { background: #3d9970; }
.legend-block.alternant { background: #b10dc9; }
.legend-block.remote { background: #85144b; }

.org-filters {
    margin-bottom: 1rem;
}
#org-breadcrumb {
    margin-bottom: 1rem;
    font-size: 1em;
    color: #555;
}
#org-chart {
    overflow-x: auto;
    padding-bottom: 2rem;
}
.org-node {
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    margin: 0.5em auto 0.5em 0;
    padding: 0.5em 1em 0.5em 1em;
    background: #fff;
    min-width: 220px;
    box-shadow: 0 2px 8px #0001;
    position: relative;
    transition: box-shadow 0.2s;
}
.org-node:hover {
    box-shadow: 0 4px 16px #0002;
    z-index: 2;
}
.org-person {
    cursor: pointer;
    padding: 0.3em 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}
.org-name {
    font-weight: bold;
    font-size: 1.1em;
}
.org-title {
    font-size: 0.95em;
    color: #666;
    margin-left: 0.2em;
}
.org-children {
    margin-left: 2em;
    border-left: 2px dashed #e0e0e0;
    padding-left: 1.5em;
    margin-top: 0.5em;
}
/* Couleurs par département (exemples, à adapter) */
.dept-rh { border-color: #0074d9; }
.dept-it { border-color: #2ecc40; }
.dept-commercial { border-color: #ff851b; }
.dept-autres { border-color: #888; }

/* Modale fiche employé */
.org-modal {
    position: fixed;
    z-index: 1000;
    left: 0; top: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}
.org-modal-content {
    background: #fff;
    border-radius: 10px;
    padding: 2em;
    min-width: 300px;
    max-width: 90vw;
    box-shadow: 0 8px 32px #0003;
    position: relative;
}
.org-modal-close {
    position: absolute;
    top: 0.7em;
    right: 1em;
    font-size: 2em;
    color: #888;
    cursor: pointer;
}
@media (max-width: 700px) {
    .org-container { padding: 0.5rem; }
    .org-node { min-width: 140px; padding: 0.5em; }
    .org-children { margin-left: 1em; padding-left: 0.5em; }
}
