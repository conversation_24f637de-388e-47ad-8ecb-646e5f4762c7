<?php
// accueil_depot_ct.php
session_start();
// Détection de la langue (GET, SESSION, ou défaut FR)
if (isset($_GET['lang'])) {
    $_SESSION['lang'] = $_GET['lang'];
}
$lang = $_SESSION['lang'] ?? 'fr';
$langFile = __DIR__ . "/lang/{$lang}.php";
if (!file_exists($langFile)) {
    $langFile = __DIR__ . "/lang/fr.php";
}
require_once($langFile);

$regions = [
    'IDF' => 'depot_IDF_ct.php',
    'Sud Ouest' => 'depot_sud_ouest.php',
    'Nord Ouest' => 'depot_nord_ouest.php',
    'Sud Est' => 'depot_sud_est.php',
    'Nord Est' => 'depot_nord_est.php'
];

if (isset($_POST['region']) && isset($regions[$_POST['region']])) {
    $_SESSION['departement'] = $_POST['region'];
    header('Location: ' . $regions[$_POST['region']]);
    exit;
}
?>
<!DOCTYPE html>
<html lang="<?= htmlspecialchars($lang) ?>">
<head>
    <meta charset="UTF-8">
    <title><?= $tr['welcome'] ?? 'Accueil dépôt CT' ?></title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body { background: #f5f5f5; }
        .accueil-container { max-width: 500px; margin: 4em auto; background: #fff; padding: 2em; border-radius: 8px; box-shadow: 0 2px 8px #ccc; text-align: center; }
        select, button { font-size: 1.1em; padding: 0.5em; margin-top: 1em; }
    </style>
</head>
<body>
<div class="accueil-container">
    <h2><?= $tr['welcome'] ?? "Bienvenue sur l'espace de dépôt CT" ?></h2>
    <a href="?lang=fr">Français</a> | <a href="?lang=en">English</a> | <a href="?lang=de">Deutsch</a>
    <form method="post">
        <label for="region">Choisissez votre région :</label><br>
        <select name="region" id="region" required>
            <option value="">-- Sélectionner --</option>
            <?php foreach ($regions as $region => $file): ?>
                <option value="<?= htmlspecialchars($region) ?>"><?= htmlspecialchars($region) ?></option>
            <?php endforeach; ?>
        </select><br>
        <button type="submit">Accéder à l'espace dépôt</button>
    </form>
</div>
</body>
</html>
