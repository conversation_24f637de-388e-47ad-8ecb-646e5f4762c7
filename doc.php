<?php
require_once __DIR__ . '/config/database.php';
session_start();
// Contrôle d'accès IT réel :
$isIT = isset($_SESSION['role']) && $_SESSION['role'] === 'IT';
// Récupérer tous les guides
$guides = [];
try {
    $stmt = $db->query('SELECT * FROM guides ORDER BY created_at DESC');
    $guides = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $guides = [];
}
?>
<div class="top-bar-fixed">
<?php include 'top_bar.php'; ?>
</div>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation d’aide utilisateur & IT</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body { font-family: Arial, sans-serif; background: #f7f7fa; margin: 0; }
        .top-bar-fixed {
            width: 100%;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            background: #2a3b8f;
            color: #fff;
            box-shadow: 0 2px 8px #0002;
        }
        .main-layout { display: flex; min-height: 100vh; margin-top: 60px; }
        .sidebar {
            width: 220px;
            background: #e8eaf6;
            padding: 32px 0 32px 0;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            border-radius: 0 20px 20px 0;
            box-shadow: 2px 0 8px #0001;
            min-height: 100vh;
            z-index: 1;
        }
        .sidebar h2 { margin-left: 32px; color: #2a3b8f; font-size: 1.2em; margin-bottom: 18px; }
        .category-list { width: 100%; }
        .category {
            background: none;
            color: #2a3b8f;
            padding: 14px 32px;
            border: none;
            text-align: left;
            width: 100%;
            font-size: 1em;
            border-radius: 0 20px 20px 0;
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
        }
        .category.active, .category:hover { background: #c5cae9; color: #111; }
        .container {
            flex: 1;
            max-width: 1100px;
            margin: 40px auto;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 8px #0001;
            padding: 32px;
        }
        h1 { color: #2a3b8f; }
        .desc { color: #444; margin-bottom: 24px; }
        .search-bar { display: flex; margin-bottom: 24px; }
        .search-bar input { flex: 1; padding: 10px; border-radius: 5px 0 0 5px; border: 1px solid #ccc; }
        .search-bar button { padding: 10px 18px; border: none; background: #2a3b8f; color: #fff; border-radius: 0 5px 5px 0; cursor: pointer; }
        .guides, .contact { margin-bottom: 32px; }
        .guide { background: #f1f3f6; padding: 16px; border-radius: 8px; margin-bottom: 16px; }
        .feedback { margin-top: 8px; }
        .feedback button { margin-right: 8px; }
        .contact a { color: #2a3b8f; text-decoration: underline; }
        @media (max-width: 900px) {
            .main-layout { flex-direction: column; margin-top: 60px; }
            .sidebar { width: 100%; border-radius: 0; box-shadow: none; flex-direction: row; padding: 0; }
            .category { border-radius: 0; padding: 12px 10px; font-size: 0.95em; }
            .container { margin: 0; border-radius: 0; }
        }
    </style>
</head>
<body>
<div class="main-layout">
    <nav class="sidebar">
        <h2>Catégories</h2>
        <div class="category-list">
            <button class="category active" data-cat="all">Toutes</button>
            <button class="category" data-cat="Réseau">Réseau</button>
            <button class="category" data-cat="Messagerie">Messagerie</button>
            <button class="category" data-cat="Logiciels">Logiciels</button>
            <button class="category" data-cat="Matériel">Matériel</button>
            <button class="category" data-cat="Accès">Accès</button>
            <button class="category" data-cat="Sécurité">Sécurité</button>
            <button class="category" data-cat="Autres">Autres</button>
        </div>
        <hr style="width:80%;margin:24px auto 12px auto;">
        <?php if ($isIT): ?>
        <a href="ajout_guide.php" class="category" style="background:#2a3b8f;color:#fff;width:80%;margin:12px auto 0 auto;padding:10px 0;border-radius:8px;text-align:center;display:block;">Administration guides</a>
        <?php endif; ?>
    </nav>
    <div class="container">
        <div id="create-guide-form" style="display:none;max-width:600px;margin:0 auto 32px auto;background:#f1f3f6;padding:24px 32px;border-radius:10px;">
            <h2>Créer un guide détaillé</h2>
            <form id="form-guide">
                <label>Titre du guide<br><input type="text" name="titre" required style="width:100%;padding:8px;margin-bottom:12px;"></label><br>
                <label>Catégorie<br>
                    <select name="categorie" required style="width:100%;padding:8px;margin-bottom:12px;">
                        <option value="Réseau">Réseau</option>
                        <option value="Messagerie">Messagerie</option>
                        <option value="Logiciels">Logiciels</option>
                        <option value="Matériel">Matériel</option>
                        <option value="Accès">Accès</option>
                        <option value="Sécurité">Sécurité</option>
                        <option value="Autres">Autres</option>
                    </select>
                </label><br>
                <label>Contenu détaillé<br><textarea name="contenu" required style="width:100%;min-height:100px;padding:8px;"></textarea></label><br>
                <button type="submit" style="margin-top:12px;background:#2a3b8f;color:#fff;padding:8px 18px;border:none;border-radius:5px;">Enregistrer le guide</button>
                <button type="button" id="cancel-create-guide" style="margin-left:8px;">Annuler</button>
            </form>
        </div>

        <h1>Documentation d’aide utilisateur & IT</h1>
        <div class="desc">
            Cette page offre un accès centralisé à des documents d’aide technique et guides pratiques, élaborés et maintenus par l’équipe IT. Elle vise à faciliter la résolution rapide des problèmes courants et à améliorer la compréhension des outils et systèmes de l’entreprise.
        </div>

        <!-- Recherche rapide par mots-clés -->
        <div class="search-bar">
            <input type="text" placeholder="Rechercher un mot-clé, un problème, un guide...">
            <button>Rechercher</button>
        </div>

        <!-- Guides détaillés (exemple dynamique) -->
        <div class="guides">
            <h2>Guides détaillés</h2>
            <?php if (empty($guides)): ?>
                <div style="color:#888;">Aucun guide pour le moment.</div>
            <?php else: ?>
                <?php foreach ($guides as $guide): ?>
                <div class="guide" data-cat="<?= htmlspecialchars($guide['categorie']) ?>" data-id="<?= $guide['id'] ?>">
                    <div class="guide-title" style="cursor:pointer;font-weight:bold;">
                        <?= htmlspecialchars($guide['titre']) ?>
                    </div>
                    <div class="guide-content" style="display:none; position:relative;">
                        <div><?= nl2br(htmlspecialchars($guide['contenu'])) ?></div>
                        <?php if (!empty($guide['pdf'])): ?>
                            <div style="margin:10px 0;">
                                <a href="pdf_doc/<?= urlencode($guide['pdf']) ?>" target="_blank" style="color:#2a3b8f;font-weight:bold;">📄 Voir le PDF associé</a>
                            </div>
                        <?php endif; ?>
                        <div class="feedback">
                            Ce guide vous a-t-il aidé ?
                            <button class="vote-btn" data-vote="up">👍 Oui (<span class="votes-up"><?= (int)($guide['votes_up'] ?? 0) ?></span>)</button>
                            <button class="vote-btn" data-vote="down">👎 Non (<span class="votes-down"><?= (int)($guide['votes_down'] ?? 0) ?></span>)</button>
                            <span class="vote-msg" style="margin-left:10px;color:#2a3b8f;"></span>
                        </div>
                        <?php if ($isIT): ?>
                        <div class="guide-actions" style="position:absolute;right:0;bottom:0;display:flex;gap:8px;padding:8px;">
                            <a href="modifier_guide.php?id=<?= $guide['id'] ?>" class="edit-guide-btn" style="text-decoration:none;">✏️</a>
                            <button class="delete-guide-btn" style="color:#b00;">🗑️</button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Contact direct support IT -->
        <div class="contact">
            <h2>Contact support IT</h2>
            <p>Besoin d’aide ? <a href="mailto:<EMAIL>">Contactez directement le support IT</a>.</p>
        </div>
    </div>
</div>
<script>
// Recherche dynamique (filtre guides)
document.querySelector('.search-bar button').onclick = function() {
    const q = document.querySelector('.search-bar input').value.toLowerCase();
    document.querySelectorAll('.guide').forEach(g => {
        g.style.display = g.textContent.toLowerCase().includes(q) ? '' : 'none';
    });
};

// Feedback utilisateur sur les guides
document.querySelectorAll('.feedback button').forEach(btn => {
    btn.onclick = function() {
        const msg = btn.textContent.includes('Oui') ? 'Merci pour votre retour !' : 'Merci, nous améliorerons ce guide.';
        btn.parentElement.innerHTML = msg;
    };
});

// Contact support IT (ouverture du client mail)
document.querySelector('.contact a').onclick = function(e) {
    e.preventDefault();
    const mailtoLink = this.getAttribute('href');
    window.location.href = mailtoLink;
};

// Filtrage par catégorie
const catBtns = document.querySelectorAll('.category');
catBtns.forEach(btn => {
    btn.onclick = function() {
        catBtns.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        const cat = btn.getAttribute('data-cat');
        document.querySelectorAll('.guide').forEach(g => {
            if(cat === 'all' || (g.getAttribute('data-cat') === cat)) {
                g.style.display = '';
            } else {
                g.style.display = 'none';
            }
        });
    };
});

// Dépliage/repliage des guides
const guideTitles = document.querySelectorAll('.guide-title');
guideTitles.forEach(title => {
    title.onclick = function() {
        const content = this.nextElementSibling;
        if(content.style.display === 'none' || content.style.display === '') {
            content.style.display = 'block';
        } else {
            content.style.display = 'none';
        }
    };
});

// Affichage du formulaire de création de guide
const btnCreateGuide = document.getElementById('btn-create-guide');
const createGuideForm = document.getElementById('create-guide-form');
const cancelCreateGuide = document.getElementById('cancel-create-guide');
if(btnCreateGuide && createGuideForm && cancelCreateGuide) {
    btnCreateGuide.onclick = function() {
        createGuideForm.style.display = 'block';
        window.scrollTo({top:0,behavior:'smooth'});
    };
    cancelCreateGuide.onclick = function() {
        createGuideForm.style.display = 'none';
    };
    document.getElementById('form-guide').onsubmit = function(e) {
        e.preventDefault();
        alert('Guide enregistré (simulation, à connecter à la base de données côté serveur).');
        createGuideForm.style.display = 'none';
        this.reset();
    };
}

// Vote sur les guides
document.querySelectorAll('.vote-btn').forEach(btn => {
    btn.onclick = function(e) {
        e.preventDefault();
        const guideDiv = btn.closest('.guide');
        const id = guideDiv.getAttribute('data-id');
        const vote = btn.getAttribute('data-vote');
        fetch('vote_guide.php', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: 'id=' + encodeURIComponent(id) + '&vote=' + encodeURIComponent(vote)
        })
        .then(r => r.json())
        .then(data => {
            if(data.success) {
                guideDiv.querySelector('.votes-up').textContent = data.votes_up;
                guideDiv.querySelector('.votes-down').textContent = data.votes_down;
                guideDiv.querySelector('.vote-msg').textContent = 'Merci pour votre retour !';
            } else {
                guideDiv.querySelector('.vote-msg').textContent = 'Erreur : ' + data.msg;
            }
        });
    };
});

// Suppression d'un guide (IT)
document.querySelectorAll('.delete-guide-btn').forEach(btn => {
    btn.onclick = function(e) {
        e.stopPropagation();
        const guideDiv = btn.closest('.guide');
        const id = guideDiv.getAttribute('data-id');
        if(confirm('Supprimer ce guide ?')) {
            fetch('supprimer_guide.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'id=' + encodeURIComponent(id)
            })
            .then(r => r.json())
            .then(data => {
                if(data.success) {
                    guideDiv.remove();
                } else {
                    alert('Erreur : ' + data.msg);
                }
            });
        }
    };
});
</script>
</body>
</html>