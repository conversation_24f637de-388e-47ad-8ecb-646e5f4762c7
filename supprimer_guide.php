<?php
require_once __DIR__ . '/config/database.php';
header('Content-Type: application/json');
// Simuler un accès IT (à remplacer par votre auth réelle)
$isIT = true;
if (!$isIT) {
    echo json_encode(['success' => false, 'msg' => 'Accès réservé aux IT.']);
    exit;
}
if (!isset($_POST['id'])) {
    echo json_encode(['success' => false, 'msg' => 'ID manquant']);
    exit;
}
$id = (int)$_POST['id'];
try {
    // Supprimer le PDF associé si présent
    $stmt = $db->prepare('SELECT pdf FROM guides WHERE id = ?');
    $stmt->execute([$id]);
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($row && !empty($row['pdf'])) {
        $pdfPath = __DIR__ . '/pdf_doc/' . $row['pdf'];
        if (file_exists($pdfPath)) @unlink($pdfPath);
    }
    // Supprimer le guide
    $db->prepare('DELETE FROM guides WHERE id = ?')->execute([$id]);
    echo json_encode(['success' => true]);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'msg' => $e->getMessage()]);
}
