<?php
session_start();
// Détection de la langue (GET, SESSION, ou défaut FR)
if (isset($_GET['lang'])) {
    $_SESSION['lang'] = $_GET['lang'];
}
$lang = $_SESSION['lang'] ?? 'fr';
$langFile = __DIR__ . "/lang/{$lang}.php";
if (!file_exists($langFile)) {
    $langFile = __DIR__ . "/lang/fr.php";
}
require_once($langFile);

// Cette page permet à ccartier de s'auto-promouvoir admin sur tout le site
if (isset($_SESSION['sAMAccountName']) && strtolower($_SESSION['sAMAccountName']) === 'ccartier') {
    $_SESSION['role'] = 'IT'; // ou un rôle "ADMIN" si tu veux
    $_SESSION['is_global_admin'] = true;
    $msg = $tr['admin_now'] ?? "Vous êtes maintenant administrateur global sur tout le site.";
} else {
    $msg = $tr['access_denied'] ?? "Accès refusé. Seul ccartier peut utiliser cette page.";
}
?>
<!DOCTYPE html>
<html lang="<?= htmlspecialchars($lang) ?>">
<head>
    <meta charset="UTF-8">
    <title><?= $tr['admin_activation'] ?? 'Admin global' ?></title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body { font-family: Arial, sans-serif; background: #f7f7fa; margin: 0; }
        .container { max-width: 500px; margin: 80px auto; background: #fff; border-radius: 10px; box-shadow: 0 2px 8px #0001; padding: 32px; text-align: center; }
        h1 { color: #2a3b8f; }
        .msg { color: #2a3b8f; font-size: 1.1em; margin-top: 24px; }
    </style>
</head>
<body>
<div class="container">
    <h1><?= $tr['admin_activation'] ?? 'Activation admin global' ?></h1>
    <div class="msg"><?php echo $msg; ?></div>
    <a href="docs_services.php?lang=<?= htmlspecialchars($lang) ?>" style="display:inline-block;margin-top:32px;color:#fff;background:#2a3b8f;padding:10px 24px;border-radius:6px;text-decoration:none;">Retour à l'accueil documentaire</a>
    <br><a href="?lang=fr">Français</a> | <a href="?lang=en">English</a> | <a href="?lang=de">Deutsch</a>
</div>
</body>
</html>
