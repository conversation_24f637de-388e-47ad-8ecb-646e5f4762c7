<?php 
include 'top-bar-informatique.php';
require_once 'config/database.php';

function loadInventaire() {
    global $db;
    $stmt = $db->query("SELECT * FROM inventaire ORDER BY created_at DESC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function addInventaire($nom_article, $type, $quantite, $description, $etat) {
    global $db;
    $sql = "INSERT INTO inventaire (nom_article, type, quantite, description, etat) VALUES (?, ?, ?, ?, ?)";
    $stmt = $db->prepare($sql);
    $stmt->execute([$nom_article, $type, $quantite, $description, $etat]);
}

function deleteInventaire($id) {
    global $db;
    $sql = "DELETE FROM inventaire WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$id]);
}

function updateQuantite($id, $quantite) {
    global $db;
    $sql = "UPDATE inventaire SET quantite = ? WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$quantite, $id]);
}

$inventaire = loadInventaire();

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['ajouter'])) {
        addInventaire($_POST['nom_article'], $_POST['type'], $_POST['quantite'], $_POST['description'], $_POST['etat']);
    } elseif (isset($_POST['supprimer'])) {
        deleteInventaire($_POST['id_supprimer']);
    } elseif (isset($_POST['modifier_quantite'])) {
        updateQuantite($_POST['id_modifier'], $_POST['quantite_modifier']);
    }
    $inventaire = loadInventaire(); // Reload updated data
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventaire du Service Informatique</title>
    <link rel="stylesheet" href="styles.css">
    <style>
    body {
        background: #f6f8fa;
        font-family: 'Segoe UI', Arial, sans-serif;
    }
    .container {
        max-width: 1100px;
        margin: 30px auto 0 auto;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 24px rgba(0,0,0,0.08);
        padding: 32px 28px 28px 28px;
    }
    .page-title {
        font-size: 2.2em;
        color: #2d3a4a;
        margin-bottom: 18px;
        letter-spacing: 1px;
        text-align: center;
    }
    .form-section {
        display: flex;
        gap: 32px;
        flex-wrap: wrap;
        margin-bottom: 32px;
        justify-content: center;
    }
    .form-card {
        background: #f9fafb;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        padding: 28px 22px 18px 22px;
        min-width: 270px;
        flex: 1 1 300px;
        max-width: 350px;
        margin-bottom: 10px;
    }
    .form-card h2 {
        font-size: 1.2em;
        color: #f57c00;
        margin-bottom: 18px;
        text-align: center;
    }
    .form-card label {
        display: block;
        margin-bottom: 4px;
        color: #2d3a4a;
        font-weight: 500;
    }
    .form-card input[type="text"],
    .form-card input[type="number"] {
        width: 100%;
        padding: 7px 10px;
        margin-bottom: 14px;
        border: 1px solid #d1d5db;
        border-radius: 5px;
        background: #fff;
        font-size: 1em;
        transition: border 0.2s;
    }
    .form-card input[type="text"]:focus,
    .form-card input[type="number"]:focus {
        border: 1.5px solid #f57c00;
        outline: none;
    }
    .btn-primary, .btn-danger {
        padding: 8px 18px;
        border-radius: 5px;
        border: none;
        font-size: 1em;
        font-weight: 500;
        cursor: pointer;
        transition: background 0.2s, box-shadow 0.2s;
        box-shadow: 0 2px 6px rgba(0,0,0,0.04);
        margin-right: 6px;
        margin-bottom: 2px;
        display: inline-flex;
        align-items: center;
        gap: 6px;
    }
    .btn-primary {
        background: #f57c00;
        color: #fff;
    }
    .btn-primary:hover {
        background: #e65100;
    }
    .btn-danger {
        background: #e74c3c;
        color: #fff;
    }
    .btn-danger:hover {
        background: #c0392b;
    }
    .table-container {
        margin-top: 18px;
        overflow-x: auto;
        background: #f9fafb;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        padding: 18px 10px 10px 10px;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        background: #fff;
        border-radius: 8px;
        overflow: hidden;
    }
    thead th {
        background: #f57c00;
        color: #fff;
        font-weight: 600;
        padding: 12px 8px;
        text-align: left;
        position: sticky;
        top: 0;
        z-index: 1;
    }
    tbody td {
        padding: 10px 8px;
        border-bottom: 1px solid #f0f0f0;
        color: #2d3a4a;
        font-size: 1em;
    }
    tbody tr:hover {
        background: #fff3e0;
    }
    @media (max-width: 900px) {
        .form-section { flex-direction: column; align-items: stretch; }
        .form-card { max-width: 100%; }
    }
    @media (max-width: 600px) {
        .container { padding: 10px 2px; }
        .form-card { padding: 14px 6px; }
        .table-container { padding: 6px 2px; }
        table, thead, tbody, th, td, tr { font-size: 0.95em; }
    }
    </style>
</head>
<body>
<div class="container">
    <h1 class="page-title">Inventaire du Service Informatique</h1>

    <!-- Form Section -->
    <div class="form-section">
        <div class="form-card">
            <h2>Ajouter un Matériel</h2>
            <form method="POST">
                <label for="nom_article">Nom de l'article :</label>
                <input type="text" id="nom_article" name="nom_article" required>

                <label for="type">Type :</label>
                <input type="text" id="type" name="type" required>

                <label for="quantite">Quantité :</label>
                <input type="number" id="quantite" name="quantite" required>

                <label for="description">Description :</label>
                <input type="text" id="description" name="description" required>

                <label for="etat">État :</label>
                <select id="etat" name="etat" required>
                    <option value="">Sélectionner...</option>
                    <option value="Neuf">Neuf</option>
                    <option value="Très bon état">Très bon état</option>
                    <option value="Bon état">Bon état</option>
                    <option value="Moyen">Moyen</option>
                    <option value="Mauvais">Mauvais</option>
                    <option value="HS">HS</option>
                </select>

                <button type="submit" name="ajouter" class="btn-primary">Ajouter</button>
            </form>
        </div>
    </div>

    <!-- Inventory Table -->
    <h2 class="section-title">Liste de l'Inventaire</h2>
    <div class="table-container">
        <div style="display:flex; justify-content:space-between; align-items:center; margin-bottom:8px; flex-wrap:wrap; gap:10px;">
            <div>
                <label for="rowsPerPage"><strong>Lignes par page :</strong></label>
                <select id="rowsPerPage" style="padding:3px 10px; border-radius:4px;">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>
            <div id="paginationControls" style="display:flex; align-items:center; gap:8px;"></div>
        </div>
        <table id="inventaireTable">
            <thead>
                <tr>
                    <th>Nom de l'article</th>
                    <th>Type</th>
                    <th>Quantité</th>
                    <th>Description</th>
                    <th>État</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($inventaire as $row): ?>
                    <tr>
                        <td><?= htmlspecialchars($row['nom_article']) ?></td>
                        <td><?= htmlspecialchars($row['type']) ?></td>
                        <td><?= htmlspecialchars($row['quantite']) ?></td>
                        <td><?= htmlspecialchars($row['description']) ?></td>
                        <td><?= htmlspecialchars($row['etat']) ?></td>
                        <td>
                            <button class="btn-danger" onclick="deleteMateriel(<?= $row['id'] ?>)">Supprimer</button>
                            <button class="btn-primary" onclick="editMateriel(<?= $row['id'] ?>)">Modifier</button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>

<?php include 'bottom_bar.php'; ?>
<script>
// Fonction utilitaire pour reload le tableau sans recharger la page
function reloadInventaireTable() {
    fetch('inventaire_ajax.php?reload=1')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.inventaireData = data.inventaire;
                renderInventaireTable();
            }
        });
}

// Pagination
let currentPage = 1;
let rowsPerPage = 10;
window.inventaireData = window.inventaireData || <?php echo json_encode($inventaire); ?>;

function renderInventaireTable() {
    const tbody = document.querySelector('#inventaireTable tbody');
    const data = window.inventaireData || [];
    rowsPerPage = parseInt(document.getElementById('rowsPerPage').value, 10);
    const totalRows = data.length;
    const totalPages = Math.max(1, Math.ceil(totalRows / rowsPerPage));
    if (currentPage > totalPages) currentPage = totalPages;
    const start = (currentPage - 1) * rowsPerPage;
    const end = start + rowsPerPage;
    tbody.innerHTML = '';
    data.slice(start, end).forEach(row => {
        tbody.innerHTML += `<tr>
            <td>${row.nom_article}</td>
            <td>${row.type}</td>
            <td>${row.quantite}</td>
            <td>${row.description}</td>
            <td>${row.etat}</td>
            <td>
                <button class='btn-danger' onclick='deleteMateriel(${row.id})'>Supprimer</button>
                <button class='btn-primary' onclick='editMateriel(${row.id})'>Modifier</button>
            </td>
        </tr>`;
    });
    renderPaginationControls(totalPages);
}

function renderPaginationControls(totalPages) {
    const controls = document.getElementById('paginationControls');
    controls.innerHTML = '';
    const prevBtn = document.createElement('button');
    prevBtn.textContent = 'Précédent';
    prevBtn.className = 'btn-primary';
    prevBtn.disabled = currentPage === 1;
    prevBtn.onclick = () => { currentPage--; renderInventaireTable(); };
    controls.appendChild(prevBtn);
    const pageInfo = document.createElement('span');
    pageInfo.textContent = `Page ${currentPage} / ${totalPages}`;
    controls.appendChild(pageInfo);
    const nextBtn = document.createElement('button');
    nextBtn.textContent = 'Suivant';
    nextBtn.className = 'btn-primary';
    nextBtn.disabled = currentPage === totalPages;
    nextBtn.onclick = () => { currentPage++; renderInventaireTable(); };
    controls.appendChild(nextBtn);
}

document.getElementById('rowsPerPage').addEventListener('change', function() {
    rowsPerPage = parseInt(this.value, 10);
    currentPage = 1;
    renderInventaireTable();
});

// Initialisation
renderInventaireTable();

// Ajout matériel AJAX
const addForm = document.querySelector('form[action=""], form button[name="ajouter"]')?.form;
if (addForm) {
    addForm.addEventListener('submit', function(e) {
        if (e.submitter && e.submitter.name === 'ajouter') {
            e.preventDefault();
            const formData = new FormData(this);
            formData.append('ajouter', '1'); // Correction ici
            fetch('inventaire_ajax.php', {
                method: 'POST',
                body: new URLSearchParams(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({ icon: 'success', title: 'Succès', text: 'Matériel ajouté.' });
                    reloadInventaireTable();
                    this.reset();
                } else {
                    Swal.fire({ icon: 'error', title: 'Erreur', text: data.error || 'Erreur lors de l\'ajout.' });
                }
            });
        }
    });
}

// Suppression matériel AJAX
const delForm = document.querySelector('form button[name="supprimer"]')?.form;
if (delForm) {
    delForm.addEventListener('submit', function(e) {
        if (e.submitter && e.submitter.name === 'supprimer') {
            e.preventDefault();
            const formData = new FormData(this);
            formData.append('supprimer', '1'); // Correction ici
            fetch('inventaire_ajax.php', {
                method: 'POST',
                body: new URLSearchParams(formData)
            })
            .then(response => response.json())
            .then(data => { // Correction ici : parenthèses manquantes
                if (data.success) {
                    Swal.fire({ icon: 'success', title: 'Succès', text: 'Matériel supprimé.' });
                    reloadInventaireTable();
                    this.reset();
                } else {
                    Swal.fire({ icon: 'error', title: 'Erreur', text: data.error || 'Erreur lors de la suppression.' });
                }
            });
        }
    });
}

// Modification quantité AJAX
const modForm = document.querySelector('form button[name="modifier_quantite"]')?.form;
if (modForm) {
    modForm.addEventListener('submit', function(e) {
        if (e.submitter && e.submitter.name === 'modifier_quantite') {
            e.preventDefault();
            const formData = new FormData(this);
            formData.append('modifier_quantite', '1'); // Correction ici
            fetch('inventaire_ajax.php', {
                method: 'POST',
                body: new URLSearchParams(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({ icon: 'success', title: 'Succès', text: 'Quantité modifiée.' });
                    reloadInventaireTable();
                    this.reset();
                } else {
                    Swal.fire({ icon: 'error', title: 'Erreur', text: data.error || 'Erreur lors de la modification.' });
                }
            });
        }
    });
}

// Bouton supprimer dans le tableau
window.deleteMateriel = function(id) {
    console.log('deleteMateriel appelé pour id', id); // debug
    Swal.fire({
        title: 'Supprimer le matériel',
        text: 'Êtes-vous sûr de vouloir supprimer ce matériel ?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Oui',
        cancelButtonText: 'Annuler'
    }).then((result) => {
        if (result.isConfirmed) {
            const formData = new URLSearchParams();
            formData.append('id_supprimer', id);
            formData.append('supprimer', '1');
            fetch('inventaire_ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({ icon: 'success', title: 'Succès', text: 'Matériel supprimé.' });
                    // Retirer la ligne du DOM immédiatement
                    const btn = document.querySelector(`button.btn-danger[onclick='deleteMateriel(${id})']`);
                    if (btn) {
                        const row = btn.closest('tr');
                        if (row) row.remove();
                    }
                } else {
                    Swal.fire({ icon: 'error', title: 'Erreur', text: data.error || 'Erreur lors de la suppression.' });
                }
            });
        }
    });
}

// Bouton modifier dans le tableau (ouvre un prompt pour la quantité)
function editMateriel(id) {
    Swal.fire({
        title: 'Modifier la quantité',
        input: 'number',
        inputLabel: 'Nouvelle quantité',
        inputAttributes: { min: 0 },
        showCancelButton: true,
        confirmButtonText: 'Enregistrer',
        cancelButtonText: 'Annuler'
    }).then((result) => {
        if (result.isConfirmed && result.value !== '') {
            const formData = new URLSearchParams();
            formData.append('id_modifier', id);
            formData.append('quantite_modifier', result.value);
            formData.append('modifier_quantite', '1');
            fetch('inventaire_ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({ icon: 'success', title: 'Succès', text: 'Quantité modifiée.' });
                    reloadInventaireTable();
                } else {
                    Swal.fire({ icon: 'error', title: 'Erreur', text: data.error || 'Erreur lors de la modification.' });
                }
            });
        }
    });
}
</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</body>
</html>
