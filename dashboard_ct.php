<?php
session_start();
// Détection de la langue (GET, SESSION, ou défaut FR)
if (isset($_GET['lang'])) {
    $_SESSION['lang'] = $_GET['lang'];
}
$lang = $_SESSION['lang'] ?? 'fr';
$langFile = __DIR__ . "/lang/{$lang}.php";
if (!file_exists($langFile)) {
    $langFile = __DIR__ . "/lang/fr.php";
}
require_once($langFile);
require_once 'config.php';

// Pour la démonstration, on utilise un ID utilisateur fixe
$current_user_id = 1;
$current_departement_id = 1;

// Traitement de l'upload de fichier
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['document'])) {
    $file = $_FILES['document'];
    $fileName = basename($file['name']);
    $fileType = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    
    // Vérifier le type de fichier
    if (!array_key_exists($fileType, ALLOWED_TYPES)) {
        $error = "Type de fichier non autorisé";
    }
    // Vérifier la taille du fichier
    elseif ($file['size'] > MAX_FILE_SIZE) {
        $error = "Le fichier est trop volumineux (max 50MB)";
    }
    else {
        // Créer un nom de fichier unique
        $newFileName = uniqid() . '_' . $fileName;
        $uploadPath = UPLOAD_DIR . $newFileName;
        
        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            // Enregistrer dans la base de données
            $stmt = $pdo->prepare("INSERT INTO documents (nom_du_fichier, chemin_fichier, type_fichier, taille_fichier, utilisateur_id, departement_id) VALUES (?, ?, ?, ?, ?, ?)");            $stmt->execute([
                $fileName,
                $newFileName,
                $fileType,
                $file['size'],
                $current_user_id,
                $current_departement_id
            ]);
            $success = "Document téléchargé avec succès";
        } else {
            $error = "Erreur lors du téléchargement du fichier";
        }
    }
}

// Récupérer les documents de l'utilisateur
$stmt = $pdo->prepare("SELECT * FROM documents WHERE utilisateur_id = ? ORDER BY date_upload DESC");
$stmt->execute([$current_user_id]);
$documents = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="<?= htmlspecialchars($lang) ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $tr['dashboard_ct'] ?? 'Tableau de bord CT' ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <a href="?lang=fr">Français</a> | <a href="?lang=en">English</a> | <a href="?lang=de">Deutsch</a>
    <?php include 'navbar.php'; ?>

    <div class="container mt-4">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        <?php if (isset($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Télécharger un document</h5>
                        <form method="POST" enctype="multipart/form-data" id="uploadForm">
                            <div class="upload-area" id="dropZone">
                                <i class="fas fa-cloud-upload-alt fa-3x mb-3"></i>
                                <p>Glissez vos fichiers ici ou cliquez pour sélectionner</p>
                                <input type="file" name="document" id="fileInput" class="d-none" accept=".pdf,.doc,.docx,.xls,.xlsx">
                            </div>
                            <button type="submit" class="btn btn-primary w-100">Télécharger</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Mes documents</h5>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Nom du fichier</th>
                                        <th>Type</th>
                                        <th>Taille</th>
                                        <th>Date d'envoi</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($documents as $doc): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($doc['nom_du_fichier']); ?></td>
                                            <td><?php echo strtoupper($doc['type_fichier']); ?></td>
                                            <td><?php echo round($doc['taille_fichier'] / 1024 / 1024, 2); ?> MB</td>
                                            <td><?php echo date('d/m/Y H:i', strtotime($doc['date_upload'])); ?></td>
                                            <td>
                                                <a href="download.php?id=<?php echo $doc['id']; ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');

        dropZone.addEventListener('click', () => fileInput.click());

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#0d6efd';
            dropZone.style.backgroundColor = '#f8f9fa';
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.style.borderColor = '#ddd';
            dropZone.style.backgroundColor = '';
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            fileInput.files = e.dataTransfer.files;
            dropZone.style.borderColor = '#ddd';
            dropZone.style.backgroundColor = '';
        });

        fileInput.addEventListener('change', () => {
            if (fileInput.files.length > 0) {
                document.getElementById('uploadForm').submit();
            }
        });
    </script>
</body>
</html>
