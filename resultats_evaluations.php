<?php
require_once('config/database.php');
// Récupérer les moyennes des prestataires
$prestataires = $db->query("SELECT p.id, p.nom, AVG(e.note) as moyenne, COUNT(e.id) as nb_eval FROM prestataires p LEFT JOIN evaluations e ON e.cible_id = p.id AND e.type = 'prestataire' GROUP BY p.id, p.nom ORDER BY moyenne DESC")->fetchAll(PDO::FETCH_ASSOC);
// Récupérer les moyennes des interlocuteurs
$interlocuteurs = $db->query("SELECT i.id, i.nom, i.prenom, AVG(e.note) as moyenne, COUNT(e.id) as nb_eval FROM interlocuteurs i LEFT JOIN evaluations e ON e.cible_id = i.id AND e.type = 'interlocuteur' GROUP BY i.id, i.nom, i.prenom ORDER BY moyenne DESC")->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <title>Résultats des Évaluations</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href='https://cdn.tailwindcss.com' rel='stylesheet'>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">Résultats des Évaluations</h1>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">Classement des Prestataires</h2>
                <table class="min-w-full mb-6">
                    <thead><tr><th class="p-2">#</th><th class="p-2">Nom</th><th class="p-2">Moyenne</th><th class="p-2">Évaluations</th></tr></thead>
                    <tbody>
                    <?php $rank=1; foreach($prestataires as $p): ?>
                        <tr>
                            <td class="p-2 text-center"><?=$rank++?></td>
                            <td class="p-2"><?= htmlspecialchars($p['nom']) ?></td>
                            <td class="p-2 text-center"><?= $p['moyenne'] !== null ? number_format($p['moyenne'],2) : '-' ?></td>
                            <td class="p-2 text-center"><?= $p['nb_eval'] ?></td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
                <canvas id="camembertPrestataires" height="200"></canvas>
            </div>
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">Classement des Interlocuteurs</h2>
                <table class="min-w-full mb-6">
                    <thead><tr><th class="p-2">#</th><th class="p-2">Nom</th><th class="p-2">Prénom</th><th class="p-2">Moyenne</th><th class="p-2">Évaluations</th></tr></thead>
                    <tbody>
                    <?php $rank=1; foreach($interlocuteurs as $i): ?>
                        <tr>
                            <td class="p-2 text-center"><?=$rank++?></td>
                            <td class="p-2"><?= htmlspecialchars($i['nom']) ?></td>
                            <td class="p-2"><?= htmlspecialchars($i['prenom']) ?></td>
                            <td class="p-2 text-center"><?= $i['moyenne'] !== null ? number_format($i['moyenne'],2) : '-' ?></td>
                            <td class="p-2 text-center"><?= $i['nb_eval'] ?></td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
                <canvas id="camembertInterlocuteurs" height="200"></canvas>
            </div>
        </div>
    </div>
    <script>
    // Données pour les camemberts
    const prestatairesLabels = <?php echo json_encode(array_column($prestataires, 'nom')); ?>;
    const prestatairesData = <?php echo json_encode(array_map(function($p){return $p['moyenne'] !== null ? round($p['moyenne'],2) : 0;}, $prestataires)); ?>;
    const interlocuteursLabels = <?php echo json_encode(array_map(function($i){return $i['nom'].' '.$i['prenom'];}, $interlocuteurs)); ?>;
    const interlocuteursData = <?php echo json_encode(array_map(function($i){return $i['moyenne'] !== null ? round($i['moyenne'],2) : 0;}, $interlocuteurs)); ?>;
    // Camembert Prestataires
    new Chart(document.getElementById('camembertPrestataires'), {
        type: 'doughnut',
        data: {
            labels: prestatairesLabels,
            datasets: [{
                data: prestatairesData,
                backgroundColor: [
                    '#f57c00','#005ea2','#e65100','#004c87','#f4f4f4','#333333','#ffb300','#1976d2','#ffa726','#1565c0'
                ]
            }]
        },
        options: {responsive:true, plugins:{legend:{position:'bottom'}}}
    });
    // Camembert Interlocuteurs
    new Chart(document.getElementById('camembertInterlocuteurs'), {
        type: 'doughnut',
        data: {
            labels: interlocuteursLabels,
            datasets: [{
                data: interlocuteursData,
                backgroundColor: [
                    '#005ea2','#f57c00','#e65100','#004c87','#f4f4f4','#333333','#ffb300','#1976d2','#ffa726','#1565c0'
                ]
            }]
        },
        options: {responsive:true, plugins:{legend:{position:'bottom'}}}
    });
    </script>
</body>
</html>
