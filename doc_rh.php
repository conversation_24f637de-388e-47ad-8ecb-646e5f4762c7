<?php
require_once __DIR__ . '/config/database.php';
session_start();
// Contrôle d'accès RH réel :
$isRH = (isset($_SESSION['role']) && ($_SESSION['role'] === 'RH' || $_SESSION['role'] === 'IT')) || (isset($_SESSION['is_global_admin']) && $_SESSION['is_global_admin']) || (isset($_SESSION['sAMAccountName']) && strtolower($_SESSION['sAMAccountName']) === 'ccartier');
// Récupérer tous les guides RH
$guides = [];
try {
    $stmt = $db->query('SELECT * FROM guides_rh ORDER BY created_at DESC');
    $guides = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $guides = [];
}
?>
<div class="top-bar-fixed">
<?php include 'top_bar.php'; ?>
</div>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation RH</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body { font-family: Arial, sans-serif; background: #f7f7fa; margin: 0; }
        .top-bar-fixed {
            width: 100%;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            background: #2a3b8f;
            color: #fff;
            box-shadow: 0 2px 8px #0002;
        }
        .main-layout { display: flex; min-height: 100vh; margin-top: 60px; }
        .sidebar {
            width: 220px;
            background: #e8eaf6;
            padding: 32px 0 32px 0;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            border-radius: 0 20px 20px 0;
            box-shadow: 2px 0 8px #0001;
            min-height: 100vh;
            z-index: 1;
        }
        .sidebar h2 { margin-left: 32px; color: #2a3b8f; font-size: 1.2em; margin-bottom: 18px; }
        .category-list { width: 100%; }
        .category {
            background: none;
            color: #2a3b8f;
            padding: 14px 32px;
            border: none;
            text-align: left;
            width: 100%;
            font-size: 1em;
            border-radius: 0 20px 20px 0;
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
        }
        .category.active, .category:hover { background: #c5cae9; color: #111; }
        .container {
            flex: 1;
            max-width: 1100px;
            margin: 40px auto;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 8px #0001;
            padding: 32px;
        }
        h1 { color: #2a3b8f; }
        .desc { color: #444; margin-bottom: 24px; }
        .search-bar { display: flex; margin-bottom: 24px; }
        .search-bar input { flex: 1; padding: 10px; border-radius: 5px 0 0 5px; border: 1px solid #ccc; }
        .search-bar button { padding: 10px 18px; border: none; background: #2a3b8f; color: #fff; border-radius: 0 5px 5px 0; cursor: pointer; }
        .guides, .contact { margin-bottom: 32px; }
        .guide { background: #f1f3f6; padding: 16px; border-radius: 8px; margin-bottom: 16px; }
        .feedback { margin-top: 8px; }
        .feedback button { margin-right: 8px; }
        .contact a { color: #2a3b8f; text-decoration: underline; }
        @media (max-width: 900px) {
            .main-layout { flex-direction: column; margin-top: 60px; }
            .sidebar { width: 100%; border-radius: 0; box-shadow: none; flex-direction: row; padding: 0; }
            .category { border-radius: 0; padding: 12px 10px; font-size: 0.95em; }
            .container { margin: 0; border-radius: 0; }
        }
    </style>
</head>
<body>
<div class="main-layout">
    <nav class="sidebar">
        <h2>Catégories</h2>
        <div class="category-list">
            <button class="category active" data-cat="all">Toutes</button>
            <button class="category" data-cat="Congés">Congés</button>
            <button class="category" data-cat="Contrats">Contrats</button>
            <button class="category" data-cat="Paie">Paie</button>
            <button class="category" data-cat="Autres">Autres</button>
        </div>
        <hr style="width:80%;margin:24px auto 12px auto;">
        <?php if ($isRH): ?>
        <a href="ajout_guide_rh.php" class="category" style="background:#2a3b8f;color:#fff;width:80%;margin:12px auto 0 auto;padding:10px 0;border-radius:8px;text-align:center;display:block;">Administration guides RH</a>
        <?php endif; ?>
    </nav>
    <div class="container">
        <h1>Documentation RH</h1>
        <div class="desc">
            Cette page regroupe les documents et procédures RH (congés, contrats, paie, etc.) à destination des collaborateurs.
        </div>
        <div class="search-bar">
            <input type="text" placeholder="Rechercher un mot-clé, un guide RH...">
            <button>Rechercher</button>
        </div>
        <div class="guides">
            <h2>Guides RH</h2>
            <?php if (empty($guides)): ?>
                <div style="color:#888;">Aucun guide RH pour le moment.</div>
            <?php else: ?>
                <?php foreach ($guides as $guide): ?>
                <div class="guide" data-cat="<?= htmlspecialchars($guide['categorie']) ?>" data-id="<?= $guide['id'] ?>">
                    <div class="guide-title" style="cursor:pointer;font-weight:bold;">
                        <?= htmlspecialchars($guide['titre']) ?>
                    </div>
                    <div class="guide-content" style="display:none; position:relative;">
                        <div><?= nl2br(htmlspecialchars($guide['contenu'])) ?></div>
                        <?php if (!empty($guide['pdf'])): ?>
                            <div style="margin:10px 0;">
                                <a href="pdf_doc/<?= urlencode($guide['pdf']) ?>" target="_blank" style="color:#2a3b8f;font-weight:bold;">📄 Voir le PDF associé</a>
                            </div>
                        <?php endif; ?>
                        <div class="feedback">
                            Ce guide vous a-t-il aidé ?
                            <button class="vote-btn" data-vote="up">👍 Oui (<span class="votes-up"><?= (int)($guide['votes_up'] ?? 0) ?></span>)</button>
                            <button class="vote-btn" data-vote="down">👎 Non (<span class="votes-down"><?= (int)($guide['votes_down'] ?? 0) ?></span>)</button>
                            <span class="vote-msg" style="margin-left:10px;color:#2a3b8f;"></span>
                        </div>
                        <?php if ($isRH): ?>
                        <div class="guide-actions" style="position:absolute;right:0;bottom:0;display:flex;gap:8px;padding:8px;">
                            <a href="modifier_guide.php?id=<?= $guide['id'] ?>" class="edit-guide-btn" style="text-decoration:none;">✏️</a>
                            <button class="delete-guide-btn" style="color:#b00;">🗑️</button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        <div class="contact">
            <h2>Contact RH</h2>
            <p>Une question RH ? <a href="mailto:<EMAIL>">Contactez directement le service RH</a>.</p>
        </div>
    </div>
</div>
<script>
// Recherche dynamique (filtre guides)
document.querySelector('.search-bar button').onclick = function() {
    const q = document.querySelector('.search-bar input').value.toLowerCase();
    document.querySelectorAll('.guide').forEach(g => {
        g.style.display = g.textContent.toLowerCase().includes(q) ? '' : 'none';
    });
};
// Feedback utilisateur sur les guides
document.querySelectorAll('.feedback button').forEach(btn => {
    btn.onclick = function() {
        const msg = btn.textContent.includes('Oui') ? 'Merci pour votre retour !' : 'Merci, nous améliorerons ce guide.';
        btn.parentElement.innerHTML = msg;
    };
});
// Contact RH (ouverture du client mail)
document.querySelector('.contact a').onclick = function(e) {
    e.preventDefault();
    const mailtoLink = this.getAttribute('href');
    window.location.href = mailtoLink;
};
// Filtrage par catégorie
const catBtns = document.querySelectorAll('.category');
catBtns.forEach(btn => {
    btn.onclick = function() {
        catBtns.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        const cat = btn.getAttribute('data-cat');
        document.querySelectorAll('.guide').forEach(g => {
            if(cat === 'all' || (g.getAttribute('data-cat') === cat)) {
                g.style.display = '';
            } else {
                g.style.display = 'none';
            }
        });
    };
});
// Dépliage/repliage des guides
const guideTitles = document.querySelectorAll('.guide-title');
guideTitles.forEach(title => {
    title.onclick = function() {
        const content = this.nextElementSibling;
        if(content.style.display === 'none' || content.style.display === '') {
            content.style.display = 'block';
        } else {
            content.style.display = 'none';
        }
    };
});
// Suppression d'un guide (RH)
document.querySelectorAll('.delete-guide-btn').forEach(btn => {
    btn.onclick = function(e) {
        e.stopPropagation();
        const guideDiv = btn.closest('.guide');
        const id = guideDiv.getAttribute('data-id');
        if(confirm('Supprimer ce guide ?')) {
            fetch('supprimer_guide.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'id=' + encodeURIComponent(id)
            })
            .then(r => r.json())
            .then(data => {
                if(data.success) {
                    guideDiv.remove();
                } else {
                    alert('Erreur : ' + data.msg);
                }
            });
        }
    };
});
</script>
</body>
</html>