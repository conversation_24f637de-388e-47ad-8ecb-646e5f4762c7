<?php
session_start();
// Détection de la langue (GET, SESSION, ou défaut FR)
if (isset($_GET['lang'])) {
    $_SESSION['lang'] = $_GET['lang'];
}
$lang = $_SESSION['lang'] ?? 'fr';

function libretranslate($text, $target, $source = 'fr') {
    if ($target === $source) return $text;
    $data = [
        'q' => $text,
        'source' => $source,
        'target' => $target,
        'format' => 'text'
    ];
    $ch = curl_init('https://libretranslate.de/translate');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    $response = curl_exec($ch);
    curl_close($ch);
    $result = json_decode($response, true);
    return $result['translatedText'] ?? $text;
}

// Map ISO for LibreTranslate
$lang_map = ['fr' => 'fr', 'en' => 'en', 'de' => 'de'];
$lt_lang = $lang_map[$lang] ?? 'fr';

require_once 'config.php';
// Pour la démonstration, on récupère toutes les entreprises
$stmt = $pdo->query("SELECT * FROM entreprises");
$entreprises = $stmt->fetchAll();

// Filtrer par entreprise si spécifié
$entreprise_id = $_GET['entreprise_id'] ?? ($entreprises[0]['id'] ?? null);

// Récupérer les documents
$query = "
    SELECT d.*, u.nom as utilisateur_nom, dept.nom as departement_nom, e.nom as entreprise_nom
    FROM documents d 
    JOIN utilisateurs u ON d.utilisateur_id = u.id 
    JOIN departements dept ON d.departement_id = dept.id 
    JOIN entreprises e ON dept.entreprise_id = e.id 
    WHERE 1=1
";
$params = [];

if ($entreprise_id) {
    $query .= " AND e.id = ?";
    $params[] = $entreprise_id;
}

$query .= " ORDER BY d.date_upload DESC";
$stmt = $pdo->prepare($query);
$stmt->execute($params);
$documents = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="<?= htmlspecialchars($lang) ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= libretranslate('Tableau de bord - Directeur Commercial', $lt_lang) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        .stats-card {
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <a href="?lang=fr">Français</a> | <a href="?lang=en">English</a> | <a href="?lang=de">Deutsch</a>
    <?php include 'top_bar.php'; ?>
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"><?= libretranslate('Filtrer par entreprise', $lt_lang) ?></h5>
                        <form method="GET" class="row g-3">
                            <div class="col-md-6">
                                <select name="entreprise_id" class="form-select" onchange="this.form.submit()">
                                    <?php foreach ($entreprises as $entreprise): ?>
                                        <option value="<?php echo $entreprise['id']; ?>" 
                                                <?php echo $entreprise['id'] == $entreprise_id ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($entreprise['nom']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mb-4">
            <?php
            // Statistiques
            $total_docs = count($documents);
            $total_size = array_sum(array_column($documents, 'taille_fichier'));
            $dept_counts = array_count_values(array_column($documents, 'departement_nom'));
            ?>
            <div class="col-md-4">
                <div class="card stats-card bg-primary text-white">
                    <div class="card-body">
                        <h6 class="card-title"><?= libretranslate('Documents totaux', $lt_lang) ?></h6>
                        <h2><?php echo $total_docs; ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card bg-success text-white">
                    <div class="card-body">
                        <h6 class="card-title"><?= libretranslate('Espace utilisé', $lt_lang) ?></h6>
                        <h2><?php echo round($total_size / 1024 / 1024, 2); ?> MB</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card bg-info text-white">
                    <div class="card-body">
                        <h6 class="card-title"><?= libretranslate('Départements actifs', $lt_lang) ?></h6>
                        <h2><?php echo count($dept_counts); ?></h2>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <h5 class="card-title"><?= libretranslate('Tous les documents', $lt_lang) ?></h5>
                <div class="table-responsive">
                    <table class="table table-striped" id="documentsTable">
                        <thead>
                            <tr>
                                <th><?= libretranslate('Entreprise', $lt_lang) ?></th>
                                <th><?= libretranslate('Département', $lt_lang) ?></th>
                                <th><?= libretranslate('Commercial', $lt_lang) ?></th>
                                <th><?= libretranslate('Nom du fichier', $lt_lang) ?></th>
                                <th><?= libretranslate('Type', $lt_lang) ?></th>
                                <th><?= libretranslate('Taille', $lt_lang) ?></th>
                                <th><?= libretranslate("Date d'envoi", $lt_lang) ?></th>
                                <th><?= libretranslate('Actions', $lt_lang) ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($documents as $doc): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($doc['entreprise_nom']); ?></td>
                                    <td><?php echo htmlspecialchars($doc['departement_nom']); ?></td>
                                    <td><?php echo htmlspecialchars($doc['utilisateur_nom']); ?></td>
                                    <td><?php echo htmlspecialchars($doc['nom_du_fichier']); ?></td>
                                    <td><?php echo strtoupper($doc['type_fichier']); ?></td>
                                    <td><?php echo round($doc['taille_fichier'] / 1024 / 1024, 2); ?> MB</td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($doc['date_upload'])); ?></td>
                                    <td>
                                        <a href="download.php?id=<?php echo $doc['id']; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#documentsTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json'
                },
                order: [[6, 'desc']]
            });
        });
    </script>
    
</body>
</html>
