<?php
$currentPage = basename($_SERVER['PHP_SELF']);
?>
<style>
    .top-bar {
        background-color: #333;
        padding: 15px;
        text-align: center;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .top-bar .logo {
        display: flex;
        align-items: center;
        gap: 10px;
        color: white;
        font-size: 1.5em;
        font-weight: bold;
        text-decoration: none;
    }

    .top-bar .logo img {
        height: 40px;
    }

    .top-bar .nav-links {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-grow: 1;
    }

    .top-bar a {
        color: white;
        text-decoration: none;
        font-size: 1.1em;
        font-weight: bold;
        padding: 10px 15px;
        border-radius: 5px;
        transition: background-color 0.3s ease;
    }

    .top-bar a:hover, .top-bar a.active {
        background-color: #f57c00;
    }    body {
        margin-top: 100px; /* Increased spacing to prevent overlap with the top bar */
        padding-top: 0 !important; /* Override any padding-top from other styles */
    }
    
    /* Ensure the top bar has proper height and spacing */
    .top-bar {
        height: 70px;
        box-sizing: border-box;
    }
    .lang-switch {
        display: flex;
        gap: 8px;
        align-items: center;
        margin-right: 20px;
    }
    .lang-switch a {
        color: #fff;
        background: #444;
        padding: 4px 10px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 1em;
        transition: background 0.2s;
    }
    .lang-switch a.active, .lang-switch a:hover {
        background: #f57c00;
    }
</style>
<header class="top-bar">
    <a href="evaluation_system.php" class="logo">
        <img src="img/logo_rgb.svg" alt="Schluter Systems Logo">
        Schluter Systems
    </a>
    <div class="nav-links">
       <a href="ajout_evaluation.php" class="<?= $currentPage === 'ajout_evaluation.php' ? 'active' : '' ?>">Ajout évaluation</a>
        <a href="ajout_prestataire.php" class="<?= $currentPage === 'ajout_prestataire.php' ? 'active' : '' ?>">Ajout Prestataire</a>
        <a href="ajout_interlocuteur.php" class="<?= $currentPage === 'ajout_interlocuteur.php' ? 'active' : '' ?>">Ajout Interlocuteur</a>
        <a href="associer_interlocuteur.php" class="<?= $currentPage === 'associer_interlocuteur.php' ? 'active' : '' ?>">Association</a>
        <a href="moyenne_satisfaction.php" class="<?= $currentPage === 'moyenne_satisfaction.php' ? 'active' : '' ?>">Satisfaction</a>
        <a href="annuaire.php" class="<?= $currentPage === 'annuaire.php' ? 'active' : '' ?>">Annuaire</a>
        <a href="login.php?logout=true" class="logout">Déconnexion</a>
    </div>
    <div class="lang-switch">
        <?php
        $lang = $_SESSION['lang'] ?? 'fr';
        $langs = ['fr' => 'Français', 'en' => 'English', 'de' => 'Deutsch'];
        foreach ($langs as $code => $label) {
            $active = ($lang === $code) ? 'active' : '';
            // Ajoute le paramètre lang à l'URL courante sans dupliquer
            $url = strtok($_SERVER["REQUEST_URI"], '?');
            $query = $_GET;
            $query['lang'] = $code;
            $link = $url . '?' . http_build_query($query);
            echo "<a href='$link' class='$active'>$label</a>";
        }
        ?>
    </div>
</header>
