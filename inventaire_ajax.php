<?php
require_once 'config/database.php';
header('Content-Type: application/json');

function loadInventaire() {
    global $db;
    $stmt = $db->query("SELECT * FROM inventaire ORDER BY created_at DESC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function addInventaire($nom_article, $type, $quantite, $description, $etat) {
    global $db;
    $sql = "INSERT INTO inventaire (nom_article, type, quantite, description, etat) VALUES (?, ?, ?, ?, ?)";
    $stmt = $db->prepare($sql);
    $stmt->execute([$nom_article, $type, $quantite, $description, $etat]);
}

function deleteInventaire($id) {
    global $db;
    $sql = "DELETE FROM inventaire WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$id]);
}

function updateQuantite($id, $quantite) {
    global $db;
    $sql = "UPDATE inventaire SET quantite = ? WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$quantite, $id]);
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['ajouter'])) {
        addInventaire($_POST['nom_article'], $_POST['type'], $_POST['quantite'], $_POST['description'], $_POST['etat']);
        echo json_encode(['success' => true]);
        exit();
    } elseif (isset($_POST['supprimer'])) {
        deleteInventaire($_POST['id_supprimer']);
        echo json_encode(['success' => true]);
        exit();
    } elseif (isset($_POST['modifier_quantite'])) {
        updateQuantite($_POST['id_modifier'], $_POST['quantite_modifier']);
        echo json_encode(['success' => true]);
        exit();
    }
}

// Pour recharger le tableau dynamiquement
if (isset($_GET['reload'])) {
    $inventaire = loadInventaire();
    echo json_encode(['success' => true, 'inventaire' => $inventaire]);
    exit();
}

echo json_encode(['success' => false, 'error' => 'Requête invalide']);
