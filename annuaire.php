<?php
require_once('config/database.php');
// Récupération des filtres
$search = trim($_GET['search'] ?? '');
//$secteur = trim($_GET['secteur'] ?? ''); // Suppression du filtre secteur
$type = $_GET['type'] ?? 'prestataire'; // Nouveau filtre
// Liste des secteurs pour les filtres
//$secteurs = $db->query("SELECT DISTINCT secteur FROM prestataires WHERE secteur IS NOT NULL AND secteur != '' ORDER BY secteur")->fetchAll(PDO::FETCH_COLUMN);
// Construction de la requête filtrée
$where = [];
$params = [];
if ($search) {
    if ($type === 'interlocuteur') {
        $where[] = "(p.nom LIKE :search OR p.prenom LIKE :search OR p.entreprise LIKE :search OR p.poste LIKE :search OR p.departement LIKE :search OR p.email LIKE :search OR p.telephone LIKE :search OR p.notes LIKE :search)";
    } else {
        $where[] = "(p.nom LIKE :search OR p.secteur LIKE :search)";
    }
    $params['search'] = "%$search%";
}
$whereSql = $where ? ('WHERE ' . implode(' AND ', $where)) : '';
if ($type === 'interlocuteur') {
    // Vérifier si la table existe avant d'exécuter la requête
    $tableExists = $db->query("SHOW TABLES LIKE 'interlocuteurs'")->fetchColumn();
    if ($tableExists) {
        $sql = "SELECT p.*, AVG(e.note) as moyenne FROM interlocuteurs p LEFT JOIN evaluations e ON e.cible_id = p.id AND e.type = 'interlocuteur' $whereSql GROUP BY p.id ORDER BY p.nom";
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $prestataires = $stmt->fetchAll(PDO::FETCH_ASSOC);
        // Récupérer les prestataires associés à chaque interlocuteur avec leur note
        $prestatairesAssocies = [];
        $res = $db->query("SELECT pi.interlocuteur_id, pr.id as prestataire_id, pr.nom, AVG(e.note) as moyenne FROM prestataire_interlocuteur pi JOIN prestataires pr ON pi.prestataire_id = pr.id LEFT JOIN evaluations e ON e.cible_id = pr.id AND e.type = 'prestataire' GROUP BY pi.interlocuteur_id, pr.id, pr.nom");
        foreach ($res as $row) {
            $nom = htmlspecialchars($row['nom']);
            $note = ($row['moyenne'] !== null) ? '★ ' . number_format($row['moyenne'],2) : '';
            $prestatairesAssocies[$row['interlocuteur_id']][] = $nom . ($note ? " ($note)" : "");
        }
    } else {
        $prestataires = [];
        $prestatairesAssocies = [];
    }
    $interlocuteursByPrestataire = [];
} else {
    $sql = "SELECT p.*, AVG(e.note) as moyenne FROM prestataires p LEFT JOIN evaluations e ON e.cible_id = p.id AND e.type = 'prestataire' $whereSql GROUP BY p.id ORDER BY p.nom";
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $prestataires = $stmt->fetchAll(PDO::FETCH_ASSOC);
    // Récupérer les interlocuteurs associés à chaque prestataire
    $interlocuteursByPrestataire = [];
    $res = $db->query("SELECT pi.prestataire_id, i.* FROM prestataire_interlocuteur pi JOIN interlocuteurs i ON pi.interlocuteur_id = i.id");
    foreach ($res as $row) {
        $interlocuteursByPrestataire[$row['prestataire_id']][] = $row;
    }
}
// Handler AJAX pour ne renvoyer que le tableau (ou le message d'absence de résultat)
if (isset($_GET['ajax']) && $_GET['ajax'] == '1') {
    if (empty($prestataires)) {
        echo '<div class="text-center text-gray-500 py-8">';
        if($type==='interlocuteur') echo 'Aucun interlocuteur trouvé.';
        else echo 'Aucun prestataire trouvé.';
        echo '</div>';
    } else {
        ?>
        <table class="min-w-full bg-white rounded-xl shadow-lg" aria-label="Liste des <?= $type==='interlocuteur' ? 'interlocuteurs' : 'prestataires' ?>">
            <caption class="sr-only">
                <?php if($type==='interlocuteur'): ?>Liste des interlocuteurs<?php else: ?>Liste des prestataires<?php endif; ?>
            </caption>
            <thead class="bg-schluter-blue text-white">
                <tr>
                    <th class="py-3 px-4 text-left">Nom</th>
                    <?php if($type==='interlocuteur'): ?><th class="py-3 px-4 text-left">Prénom</th><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><th class="py-3 px-4 text-left">Entreprise</th><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><th class="py-3 px-4 text-left">Poste</th><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><th class="py-3 px-4 text-left">Département</th><?php endif; ?>
                    <th class="py-3 px-4 text-left">Email</th>
                    <th class="py-3 px-4 text-left">Téléphone</th>
                    <?php if($type==='interlocuteur'): ?><th class="py-3 px-4 text-left">Notes supplémentaires</th><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><th class="py-3 px-4 text-left">Note</th><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><th class="py-3 px-4 text-left">Prestataires associés</th><?php endif; ?>
                    <?php if($type==='prestataire'): ?><th class="py-3 px-4 text-left">Note</th><?php endif; ?>
                    <?php if($type==='prestataire'): ?><th class="py-3 px-4 text-left">Document</th><?php endif; ?>
                    <?php if($type==='prestataire'): ?><th class="py-3 px-4 text-left">Interlocuteurs associés</th><?php endif; ?>
                </tr>
            </thead>
            <tbody>
                <?php foreach($prestataires as $p): ?>
                <tr class="hover:bg-schluter-gray-light transition">
                    <td class="py-3 px-4 font-bold text-schluter-blue"><?= htmlspecialchars($p['nom'] ?? '') ?></td>
                    <?php if($type==='interlocuteur'): ?><td class="py-3 px-4"><?= htmlspecialchars($p['prenom'] ?? '') ?></td><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><td class="py-3 px-4"><?= htmlspecialchars($p['entreprise'] ?? '') ?></td><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><td class="py-3 px-4"><?= htmlspecialchars($p['poste'] ?? '') ?></td><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><td class="py-3 px-4"><?= htmlspecialchars($p['departement'] ?? '') ?></td><?php endif; ?>
                    <td class="py-3 px-4">
                        <a href="mailto:<?= htmlspecialchars($p['email'] ?? '') ?>" class="underline text-blue-700"><?= htmlspecialchars($p['email'] ?? '') ?></a>
                    </td>
                    <td class="py-3 px-4 whitespace-nowrap"><?= htmlspecialchars($p['telephone'] ?? '') ?></td>
                    <?php if($type==='interlocuteur'): ?><td class="py-3 px-4"><?= htmlspecialchars($p['notes'] ?? '') ?></td><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?>
                    <td class="py-3 px-4 text-yellow-500 font-bold">
                        <?php if(isset($p['moyenne']) && $p['moyenne']!==null): ?>
                            ★ <?= number_format($p['moyenne'],2) ?>
                        <?php endif; ?>
                    </td>
                    <td class="py-3 px-4">
                        <?php if(!empty($prestatairesAssocies[$p['id']])): ?>
                <?= implode(', ', $prestatairesAssocies[$p['id']]) ?>
            <?php else: ?>
                <span class="text-gray-400">Aucun prestataire</span>
            <?php endif; ?>
        </td>
        <?php endif; ?>
                    <?php if($type==='prestataire'): ?>
                    <td class="py-3 px-4 text-yellow-500 font-bold">
                        <?php if(isset($p['moyenne']) && $p['moyenne']!==null): ?>
                            ★ <?= number_format($p['moyenne'],2) ?>
                        <?php endif; ?>
                    </td>
                    <td class="py-3 px-4">
                        <?php if(!empty($p['pdf']) && file_exists(__DIR__ . '/pdf_doc/' . $p['pdf'])): ?>
                            <a href="pdf_doc/<?= htmlspecialchars($p['pdf']) ?>" class="text-blue-600 underline" target="_blank">📁 Document</a>
                        <?php elseif(!empty($p['pdf'])): ?>
                            <span class="text-gray-400">Document manquant</span>
                        <?php endif; ?>
                    </td>
                    <td class="py-3 px-4">
                        <?php if(!empty($interlocuteursByPrestataire[$p['id']])): ?>
                            <ul class="list-disc pl-5">
                                <?php foreach($interlocuteursByPrestataire[$p['id']] as $i): ?>
                                    <li><?= htmlspecialchars(($i['prenom'] ?? '').' '.($i['nom'] ?? '')) ?> (<?= htmlspecialchars($i['email'] ?? '') ?>, <?= htmlspecialchars($i['telephone'] ?? '') ?>)</li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <span class="text-gray-400">Aucun interlocuteur</span>
                        <?php endif; ?>
                    </td>
                    <?php endif; ?>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php
    }
    exit;
}
?>
<?php include 'top_bar_eval_IT.php'; ?>
<main class="container mx-auto px-4 py-8">
    <h1 class="text-4xl font-bold text-center mb-10 text-schluter-blue">Annuaire</h1>
    <form method="get" class="flex flex-wrap gap-4 justify-center mb-8" aria-label="Recherche dans l'annuaire">
        <label for="search" class="sr-only">Recherche</label>
        <input id="search" type="text" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="Recherche nom, entreprise..." class="border px-4 py-2 rounded w-64">
        <label for="type" class="sr-only">Type</label>
        <select id="type" name="type" class="border px-4 py-2 rounded">
            <option value="prestataire" <?= $type==='prestataire'?'selected':'' ?>>Prestataires</option>
            <option value="interlocuteur" <?= $type==='interlocuteur'?'selected':'' ?>>Interlocuteurs</option>
        </select>
   
    </form>
    <div class="overflow-x-auto">
        <?php if (empty($prestataires)): ?>
            <div class="text-center text-gray-500 py-8">
                <?php if($type==='interlocuteur'): ?>Aucun interlocuteur trouvé.<?php else: ?>Aucun prestataire trouvé.<?php endif; ?>
            </div>
        <?php else: ?>
        <table class="min-w-full bg-white rounded-xl shadow-lg" aria-label="Liste des <?= $type==='interlocuteur' ? 'interlocuteurs' : 'prestataires' ?>">
            <caption class="sr-only">
                <?php if($type==='interlocuteur'): ?>Liste des interlocuteurs<?php else: ?>Liste des prestataires<?php endif; ?>
            </caption>
            <thead class="bg-schluter-blue text-white">
                <tr>
                    <th class="py-3 px-4 text-left">Nom</th>
                    <?php if($type==='interlocuteur'): ?><th class="py-3 px-4 text-left">Prénom</th><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><th class="py-3 px-4 text-left">Entreprise</th><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><th class="py-3 px-4 text-left">Poste</th><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><th class="py-3 px-4 text-left">Département</th><?php endif; ?>
                    <th class="py-3 px-4 text-left">Email</th>
                    <th class="py-3 px-4 text-left">Téléphone</th>
                    <?php if($type==='interlocuteur'): ?><th class="py-3 px-4 text-left">Notes supplémentaires</th><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><th class="py-3 px-4 text-left">Note</th><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><th class="py-3 px-4 text-left">Prestataires associés</th><?php endif; ?>
                    <?php if($type==='prestataire'): ?><th class="py-3 px-4 text-left">Note</th><?php endif; ?>
                    <?php if($type==='prestataire'): ?><th class="py-3 px-4 text-left">Document</th><?php endif; ?>
                    <?php if($type==='prestataire'): ?><th class="py-3 px-4 text-left">Interlocuteurs associés</th><?php endif; ?>
                    <!-- Suppression de la colonne Actions -->
                </tr>
            </thead>
            <tbody>
                <?php foreach($prestataires as $p): ?>
                <tr class="hover:bg-schluter-gray-light transition">
                    <td class="py-3 px-4 font-bold text-schluter-blue"><?= htmlspecialchars($p['nom'] ?? '') ?></td>
                    <?php if($type==='interlocuteur'): ?><td class="py-3 px-4"><?= htmlspecialchars($p['prenom'] ?? '') ?></td><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><td class="py-3 px-4"><?= htmlspecialchars($p['entreprise'] ?? '') ?></td><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><td class="py-3 px-4"><?= htmlspecialchars($p['poste'] ?? '') ?></td><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?><td class="py-3 px-4"><?= htmlspecialchars($p['departement'] ?? '') ?></td><?php endif; ?>
                    <td class="py-3 px-4">
                        <a href="mailto:<?= htmlspecialchars($p['email'] ?? '') ?>" class="underline text-blue-700"><?= htmlspecialchars($p['email'] ?? '') ?></a>
                    </td>
                    <td class="py-3 px-4 whitespace-nowrap"><?= htmlspecialchars($p['telephone'] ?? '') ?></td>
                    <?php if($type==='interlocuteur'): ?><td class="py-3 px-4"><?= htmlspecialchars($p['notes'] ?? '') ?></td><?php endif; ?>
                    <?php if($type==='interlocuteur'): ?>
                    <td class="py-3 px-4 text-yellow-500 font-bold">
                        <?php if(isset($p['moyenne']) && $p['moyenne']!==null): ?>
                            ★ <?= number_format($p['moyenne'],2) ?>
                        <?php endif; ?>
                    </td>
                    <td class="py-3 px-4">
                        <?php if(!empty($prestatairesAssocies[$p['id']])): ?>
                <?= implode(', ', $prestatairesAssocies[$p['id']]) ?>
            <?php else: ?>
                <span class="text-gray-400">Aucun prestataire</span>
            <?php endif; ?>
        </td>
        <?php endif; ?>
                    <?php if($type==='prestataire'): ?>
                    <td class="py-3 px-4 text-yellow-500 font-bold">
                        <?php if(isset($p['moyenne']) && $p['moyenne']!==null): ?>
                            ★ <?= number_format($p['moyenne'],2) ?>
                        <?php endif; ?>
                    </td>
                    <td class="py-3 px-4">
                        <?php if(!empty($p['pdf']) && file_exists(__DIR__ . '/pdf_doc/' . $p['pdf'])): ?>
                            <a href="pdf_doc/<?= htmlspecialchars($p['pdf']) ?>" class="text-blue-600 underline" target="_blank">📁 Document</a>
                        <?php elseif(!empty($p['pdf'])): ?>
                            <span class="text-gray-400">Document manquant</span>
                        <?php endif; ?>
                    </td>
                    <td class="py-3 px-4">
                        <?php if(!empty($interlocuteursByPrestataire[$p['id']])): ?>
                            <ul class="list-disc pl-5">
                                <?php foreach($interlocuteursByPrestataire[$p['id']] as $i): ?>
                                    <li><?= htmlspecialchars(($i['prenom'] ?? '').' '.($i['nom'] ?? '')) ?> (<?= htmlspecialchars($i['email'] ?? '') ?>, <?= htmlspecialchars($i['telephone'] ?? '') ?>)</li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <span class="text-gray-400">Aucun interlocuteur</span>
                        <?php endif; ?>
                    </td>
                    <?php endif; ?>
                    <!-- Suppression de la colonne Actions -->
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php endif; ?>
    </div>
</main>
<style>
.bg-schluter-blue { background: #178899; }
.bg-schluter-orange { background: #f57c00; }
.text-schluter-blue { color: #178899; }
.text-schluter-orange { color: #f57c00; }
.hover\:bg-schluter-gray-light:hover { background: #f4f4f4; }
table { border-collapse: separate; border-spacing: 0; width: 100%; background: #f8f8f8; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.05); border: 2px solid #178899; }
thead tr { background: #178899; color: #fff; }
th, td { border-bottom: 1px solid #b5d2d6; border-right: 1px solid #b5d2d6; padding: 12px 16px; }
th:last-child, td:last-child { border-right: none; }
th { font-weight: bold; font-size: 1.1em; }
tbody tr:nth-child(even) { background: #eaf3f4; }
tbody tr:nth-child(odd) { background: #fff; }
tbody tr { transition: background 0.2s; }
tbody tr:hover { background: #d0e7ea; }
table caption { padding: 8px; font-size: 1.2em; color: #178899; font-weight: bold; }
th:first-child, td:first-child { border-top-left-radius: 10px; }
th:last-child, td:last-child { border-top-right-radius: 10px; }
</style>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search');
    const typeSelect = document.getElementById('type');
    const form = searchInput.closest('form');
    const tableContainer = document.querySelector('.overflow-x-auto');

    // Fonction pour charger le tableau dynamiquement
    function fetchTable() {
        const params = new URLSearchParams({
            search: searchInput.value,
            type: typeSelect.value,
            ajax: 1
        });
        fetch(window.location.pathname + '?' + params.toString(), { headers: { 'X-Requested-With': 'XMLHttpRequest' } })
            .then(r => r.text())
            .then(html => {
                tableContainer.innerHTML = html;
                attachLiveSearch(); // Réattacher la recherche live sur le nouveau tableau
            });
    }

    // Recherche live sur le tableau
    function attachLiveSearch() {
        const table = document.querySelector('table');
        if (!searchInput || !table) return;
        const tbody = table.querySelector('tbody');
        if (!tbody) return;
        const rows = Array.from(tbody.querySelectorAll('tr'));
        searchInput.removeEventListener('input', searchHandler); // Nettoyage
        searchInput.addEventListener('input', searchHandler);
        function searchHandler() {
            const query = this.value.trim().toLowerCase();
            rows.forEach(row => {
                const rowText = Array.from(row.querySelectorAll('td'))
                    .map(td => td.textContent.toLowerCase())
                    .join(' ');
                if (!query || rowText.includes(query)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
    }

    // Live changement du select
    typeSelect.addEventListener('change', function(e) {
        fetchTable();
    });

    // Empêcher le submit classique du formulaire
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        fetchTable();
    });

    // Initialiser la recherche live
    attachLiveSearch();
});
</script>