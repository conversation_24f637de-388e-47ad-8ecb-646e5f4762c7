// Organigramme interactif - JS
$(function() {
    // Repli/zoom des blocs enfants
    $('#org-chart .org-node').each(function() {
        var $node = $(this);
        if ($node.find('>.org-children').length) {
            $node.prepend('<span class="org-toggle" title="Replier/Déplier">&#x25BC;</span> ');
        }
    });
    $(document).on('click', '.org-toggle', function(e) {
        e.stopPropagation();
        var $children = $(this).siblings('.org-children');
        if ($children.is(':visible')) {
            $children.slideUp(200);
            $(this).html('&#x25B6;');
        } else {
            $children.slideDown(200);
            $(this).html('&#x25BC;');
        }
    });
    // Fiche employé modale
    $(document).on('click', '.org-person', function(e) {
        e.stopPropagation();
        var name = $(this).data('name');
        var title = $(this).data('title');
        var mail = $(this).data('mail');
        var dept = $(this).data('dept');
        var company = $(this).data('company');
        var html = '<h2>'+name+'</h2>';
        if(title) html += '<div><b>Poste :</b> '+title+'</div>';
        if(dept) html += '<div><b>Département :</b> '+dept+'</div>';
        if(company) html += '<div><b>Entreprise :</b> '+company+'</div>';
        if(mail) html += '<div><b>Email :</b> <a href="mailto:'+mail+'">'+mail+'</a></div>';
        $('#org-modal-body').html(html);
        $('#org-modal').fadeIn(150);
    });
    $('.org-modal-close, #org-modal').on('click', function(e) {
        if(e.target === this) $('#org-modal').fadeOut(100);
    });
    // Filtre dynamique par département
    $('#org-filter-dept').on('change', function() {
        var val = $(this).val();
        if(!val) {
            $('#org-chart .org-node').show();
        } else {
            $('#org-chart .org-node').each(function() {
                var dept = $(this).data('dept');
                if(dept === val) $(this).show();
                else $(this).hide();
            });
        }
        updateBreadcrumb();
    });
    // Fil d'Ariane dynamique
    function updateBreadcrumb() {
        var path = [];
        var $visible = $('#org-chart .org-node:visible').first();
        while($visible.length) {
            var name = $visible.find('>.org-person .org-name').text();
            if(name) path.unshift(name);
            $visible = $visible.parents('.org-node').first();
        }
        if(path.length > 0) {
            $('#org-breadcrumb').html('Vous êtes ici : ' + path.join(' &rarr; '));
        } else {
            $('#org-breadcrumb').empty();
        }
    }
    updateBreadcrumb();
    // Mini-carte navigation (simple version)
    // Peut être enrichie avec une vue SVG ou canvas si besoin
});
