<?php
session_start();
// Détection de la langue (GET, SESSION, ou défaut FR)
if (isset($_GET['lang'])) {
    $_SESSION['lang'] = $_GET['lang'];
}
$lang = $_SESSION['lang'] ?? 'fr';
$langFile = __DIR__ . "/lang/{$lang}.php";
if (!file_exists($langFile)) {
    $langFile = __DIR__ . "/lang/fr.php";
}
require_once($langFile);
?>
<!-- navbar.php -->
<nav class="navbar">
    <div class="navbar-container">
        <a href="index.php?lang=<?= htmlspecialchars($lang) ?>" class="navbar-logo">
            <i class="fas fa-building"></i> Schluter Systems
        </a>
        <ul class="navbar-menu">
            <li><a href="index.php?lang=<?= htmlspecialchars($lang) ?>" <?php echo ($_SERVER['PHP_SELF'] == '/projet/index.php') ? 'class="active"' : ''; ?>><i class="fas fa-home"></i> <?= $tr['nav_home'] ?? 'Accueil' ?></a></li>
            <li><a href="assistance.php?lang=<?= htmlspecialchars($lang) ?>" <?php echo ($_SERVER['PHP_SELF'] == '/projet/assistance.php') ? 'class="active"' : ''; ?>><i class="fas fa-life-ring"></i> <?= $tr['nav_assistance'] ?? 'Assistance' ?></a></li>
            <li><a href="dashboard.php?lang=<?= htmlspecialchars($lang) ?>" <?php echo ($_SERVER['PHP_SELF'] == '/projet/dashboard.php') ? 'class="active"' : ''; ?>><i class="fas fa-tachometer-alt"></i> <?= $tr['nav_dashboard'] ?? 'Tableau de Bord' ?></a></li>
            <li><a href="events.php?lang=<?= htmlspecialchars($lang) ?>" <?php echo ($_SERVER['PHP_SELF'] == '/projet/events.php') ? 'class="active"' : ''; ?>><i class="fas fa-calendar-alt"></i> <?= $tr['nav_events'] ?? 'Événements' ?></a></li>
            <li><a href="evaluation.php?lang=<?= htmlspecialchars($lang) ?>" <?php echo ($_SERVER['PHP_SELF'] == '/projet/evaluation.php') ? 'class="active"' : ''; ?>><i class="fas fa-star"></i> <?= $tr['nav_evaluation'] ?? 'Évaluation' ?></a></li>
            <li><a href="historique_evaluations.php?lang=<?= htmlspecialchars($lang) ?>" <?php echo ($_SERVER['PHP_SELF'] == '/projet/historique_evaluations.php') ? 'class="active"' : ''; ?>><i class="fas fa-history"></i> <?= $tr['nav_history'] ?? 'Historique' ?></a></li>
            <li><a href="moyenne_satisfaction.php?lang=<?= htmlspecialchars($lang) ?>" <?php echo ($_SERVER['PHP_SELF'] == '/projet/moyenne_satisfaction.php') ? 'class="active"' : ''; ?>><i class="fas fa-chart-line"></i> <?= $tr['nav_average'] ?? 'Moyenne' ?></a></li>
            <li><a href="inventaire_du_service_info.php?lang=<?= htmlspecialchars($lang) ?>" <?php echo ($_SERVER['PHP_SELF'] == '/projet/inventaire_du_service_info.php') ? 'class="active"' : ''; ?>><i class="fas fa-boxes"></i> <?= $tr['nav_inventory'] ?? 'Inventaire' ?></a></li>
        </ul>
    </div>
</nav>
