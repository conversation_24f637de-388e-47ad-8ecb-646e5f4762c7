<?php
session_start();
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in']) {
    header('Location: login.php');
    exit();
}
require_once('config/database.php');

// Ajout d'une tâche
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['titre'], $_POST['description'], $_POST['ticket'])) {
    $titre = trim($_POST['titre']);
    $description = trim($_POST['description']);
    $ticket = trim($_POST['ticket']);
    $auteur = $_SESSION['username'] ?? 'Inconnu';
    $stmt = $db->prepare('INSERT INTO taches_it (titre, description, ticket, auteur, date_creation) VALUES (?, ?, ?, ?, NOW())');
    $stmt->execute([$titre, $description, $ticket, $auteur]);
    header('Location: tache_it.php');
    exit();
}

// Récupérer les tâches
$taches = $db->query('SELECT * FROM taches_it ORDER BY date_creation DESC')->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tâches IT</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
<?php include 'top_bar.php'; ?>
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6 text-schluter-blue">Gestion des Tâches IT</h1>
    <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">Ajouter une nouvelle tâche</h2>
        <form method="post" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label class="block mb-1 font-medium">Titre</label>
                <input type="text" name="titre" required class="w-full border rounded px-3 py-2">
            </div>
            <div>
                <label class="block mb-1 font-medium">Numéro de ticket Omnitracker</label>
                <input type="text" name="ticket" required class="w-full border rounded px-3 py-2">
            </div>
            <div class="md:col-span-2">
                <label class="block mb-1 font-medium">Description</label>
                <textarea name="description" required class="w-full border rounded px-3 py-2" rows="3"></textarea>
            </div>
            <div class="md:col-span-2 text-right">
                <button type="submit" class="bg-schluter-orange text-white px-6 py-2 rounded hover:bg-orange-600"><i class="fas fa-plus mr-2"></i>Ajouter</button>
            </div>
        </form>
    </div>
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-semibold mb-4">Liste des tâches</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full border">
                <thead>
                    <tr class="bg-schluter-orange text-white">
                        <th class="px-4 py-2">Titre</th>
                        <th class="px-4 py-2">Description</th>
                        <th class="px-4 py-2">Ticket Omnitracker</th>
                        <th class="px-4 py-2">Créé par</th>
                        <th class="px-4 py-2">Date</th>
                    </tr>
                </thead>
                <tbody>
                <?php foreach ($taches as $tache): ?>
                    <tr class="border-b hover:bg-gray-50">
                        <td class="px-4 py-2 font-semibold"><?= htmlspecialchars($tache['titre']) ?></td>
                        <td class="px-4 py-2"><?= nl2br(htmlspecialchars($tache['description'])) ?></td>
                        <td class="px-4 py-2"><?= htmlspecialchars($tache['ticket']) ?></td>
                        <td class="px-4 py-2"><?= htmlspecialchars($tache['auteur']) ?></td>
                        <td class="px-4 py-2 text-sm text-gray-500"><?= htmlspecialchars($tache['date_creation']) ?></td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php include 'bottom_bar.php'; ?>
</body>
</html>
