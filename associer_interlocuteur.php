<?php
require_once('config/database.php');
// Récupérer les prestataires
$prestataires = $db->query("SELECT id, nom FROM prestataires ORDER BY nom")->fetchAll(PDO::FETCH_ASSOC);
// Récupérer les interlocuteurs
$interlocuteurs = $db->query("SELECT id, nom, prenom FROM interlocuteurs ORDER BY nom, prenom")->fetchAll(PDO::FETCH_ASSOC);
?>
<?php include 'top_bar_eval_IT.php'; ?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Associer Interlocuteur à Prestataire</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f8f9fa; margin: 40px; }
        .form-container { background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #ddd; padding: 32px; max-width: 500px; margin: auto; }
        label { display: block; margin-top: 16px; font-weight: bold; }
        select { width: 100%; padding: 8px; margin-top: 4px; border-radius: 4px; border: 1px solid #ccc; }
        button { margin-top: 24px; padding: 10px 24px; background: #005ea2; color: #fff; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #003d6b; }
    </style>
</head>
<body>
    <div style="margin-bottom:24px;">
        <a href="evaluation_system.php" style="display:inline-block;padding:10px 22px;background:#005ea2;color:#fff;border-radius:5px;text-decoration:none;font-weight:bold;">&larr; Retour à l'accueil</a>
    </div>
    <div class="form-container">
        <h1>Associer un Interlocuteur à un Prestataire</h1>
        <form method="post" action="">
            <label for="prestataire">Prestataire *</label>
            <select id="prestataire" name="prestataire_id" required>
                <option value="">Sélectionner...</option>
                <?php foreach ($prestataires as $p) {
                    echo '<option value="'.$p['id'].'">'.htmlspecialchars($p['nom']).'</option>';
                } ?>
            </select>

            <label for="interlocuteur">Interlocuteur *</label>
            <select id="interlocuteur" name="interlocuteur_id" required>
                <option value="">Sélectionner...</option>
                <?php foreach ($interlocuteurs as $i) {
                    echo '<option value="'.$i['id'].'">'.htmlspecialchars($i['nom'].' '.$i['prenom']).'</option>';
                } ?>
            </select>

            <button type="submit">Associer</button>
        </form>
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $prestataire_id = $_POST['prestataire_id'];
            $interlocuteur_id = $_POST['interlocuteur_id'];
            if ($prestataire_id && $interlocuteur_id) {
                $sql = "INSERT INTO prestataire_interlocuteur (prestataire_id, interlocuteur_id) VALUES (?, ?)";
                $stmt = $db->prepare($sql);
                $stmt->execute([$prestataire_id, $interlocuteur_id]);
                echo '<p style="color:green;">Association enregistrée !</p>';
            }
        }
        ?>
    </div>
</body>
</html>
