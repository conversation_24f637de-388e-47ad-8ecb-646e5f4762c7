# Fonctionnalités principales des fichiers

## login.php
- Authentification des utilisateurs via LDAP (Active Directory).
- Gestion de la langue de l'interface Français,Anglais et Allemand (en cours de développement).
- Redirection automatique si l'utilisateur est déjà connecté.
- Gestion de la déconnexion.

**English:**
- User authentication via LDAP (Active Directory).
- Interface language management: French, English, and German (in development).
- Automatic redirection if the user is already logged in.
- Logout management.

## index.php
- Page d'accueil du portail intranet.
- Affichage dynamique des services disponibles (cartes avec icônes, titres, descriptions).
- Recherche de services par nom ou description.
- Gestion multilingue Français,Anglais et Allemand (en cours de développement).
- Affichage d'informations sur l'entreprise (bannière, à propos).
- Navigation haut/bas via `top_bar.php` et `bottom_bar.php`.

**English:**
- Intranet portal home page.
- Dynamic display of available services (cards with icons, titles, descriptions).
- Service search by name or description.
- Multilingual management: French, English, and German (in development).
- Display of company information (banner, about section).
- Top/bottom navigation via `top_bar.php` and `bottom_bar.php`.

## accuse_reception.php
- Création et gestion des accusés de réception.
- Affichage de la liste des accusés de réception existants.
- Possibilité de générer des documents PDF d'accusé de réception.
- Gestion multilingue Français,Anglais et Allemand (en cours de développement).

**English:**
- Creation and management of delivery receipts.
- Display of the list of existing delivery receipts.
- Ability to generate PDF documents for delivery receipts.
- Multilingual management: French, English, and German (in development).