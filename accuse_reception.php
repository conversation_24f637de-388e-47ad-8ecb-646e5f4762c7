<?php
require_once('config/database.php');
// Suppression d'un accusé de réception par ID
function deleteAccuseReception($id) {
    global $db;
    $sql = "DELETE FROM accuses_reception WHERE id = ?";
    $stmt = $db->prepare($sql);
    return $stmt->execute([$id]);
}
// Gestion de la suppression AJAX (retourne JSON)
if (isset($_POST['ajax_delete_id'])) {
    $deleteId = intval($_POST['ajax_delete_id']);
    $success = false;
    if ($deleteId > 0) {
        $success = deleteAccuseReception($deleteId);
    }
    header('Content-Type: application/json');
    echo json_encode(['success' => $success]);
    exit;
}

// Gestion de l'ajout AJAX d'un AR (retourne JSON)
if (isset($_POST['ajax_add_ar'])) {
    $collaborateur = trim($_POST['Collaborateur'] ?? '');
    $responsable = trim($_POST['Responsable'] ?? '');
    $date = trim($_POST['Date'] ?? '');
    $remis = trim($_POST['Remis'] ?? '');
    $recu = trim($_POST['Reçu'] ?? '');
    $success = false;
    $message = '';
    if ($collaborateur === '' || $responsable === '' || $date === '') {
        $message = "Merci de remplir tous les champs obligatoires (collaborateur, responsable, date).";
    } else {
        $success = addAccuseReception($collaborateur, $responsable, $date, $remis, $recu);
        $message = $success ? "Accusé de réception ajouté avec succès." : "Erreur lors de l'ajout de l'accusé de réception.";
    }
    header('Content-Type: application/json');
    echo json_encode(['success' => $success, 'message' => $message]);
    exit;
}


// Fonction pour mettre en évidence les termes recherchés
function highlightSearchTerm($text, $searchTerm) {
    if (empty($searchTerm)) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
    $escapedText = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    $escapedSearchTerm = htmlspecialchars($searchTerm, ENT_QUOTES, 'UTF-8');
    $pattern = '/' . preg_quote($escapedSearchTerm, '/') . '/i';
    return preg_replace($pattern, '<span class="highlight">$0</span>', $escapedText);
}


// Définir l'encodage par défaut pour PHP
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

include 'top-bar-informatique.php';
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once('lib/FPDF/fpdf.php');
require_once('config/database.php');

// Classe FPDF étendue pour supporter l'UTF-8 avec améliorations visuelles
class FPDF_UTF8 extends FPDF
{
    // Tableau de conversion des caractères UTF-8 vers ISO-8859-1
    private $utf8_to_iso = array(
        'à' => 'à', 'á' => 'á', 'â' => 'â', 'ã' => 'ã', 'ä' => 'ä', 'å' => 'å',
        'è' => 'è', 'é' => 'é', 'ê' => 'ê', 'ë' => 'ë',
        'ì' => 'ì', 'í' => 'í', 'î' => 'î', 'ï' => 'ï',
        'ò' => 'ò', 'ó' => 'ó', 'ô' => 'ô', 'õ' => 'õ', 'ö' => 'ö',
        'ù' => 'ù', 'ú' => 'ú', 'û' => 'û', 'ü' => 'ü',
        'ç' => 'ç', 'ñ' => 'ñ',
        'À' => 'À', 'Á' => 'Á', 'Â' => 'Â', 'Ã' => 'Ã', 'Ä' => 'Ä', 'Å' => 'Å',
        'È' => 'È', 'É' => 'É', 'Ê' => 'Ê', 'Ë' => 'Ë',
        'Ì' => 'Ì', 'Í' => 'Í', 'Î' => 'Î', 'Ï' => 'Ï',
        'Ò' => 'Ò', 'Ó' => 'Ó', 'Ô' => 'Ô', 'Õ' => 'Õ', 'Ö' => 'Ö',
        'Ù' => 'Ù', 'Ú' => 'Ú', 'Û' => 'Û', 'Ü' => 'Ü',
        'Ç' => 'Ç', 'Ñ' => 'Ñ',
        '€' => '€', '£' => '£', '¥' => '¥', '§' => '§', '©' => '©', '®' => '®',
        '°' => '°', '±' => '±', '²' => '²', '³' => '³', 'µ' => 'µ', '¶' => '¶',
        '¼' => '¼', '½' => '½', '¾' => '¾'
    );

    // Couleurs optimisées pour impression couleur ET noir/blanc
    private $colors = array(
        'primary' => array(0, 70, 150),      // Bleu Schlüter (bon contraste N&B)
        'secondary' => array(80, 80, 80),    // Gris foncé (remplace orange pour N&B)
        'text_dark' => array(0, 0, 0),       // Noir pur (contraste maximal)
        'text_light' => array(100, 100, 100), // Gris moyen (lisible en N&B)
        'border' => array(150, 150, 150),    // Gris bordure (visible en N&B)
        'background' => array(245, 245, 245) // Gris très clair (économie encre)
    );

    // Couleurs alternatives pour impression couleur uniquement
    private $colorColors = array(
        'primary' => array(0, 70, 150),      // Bleu Schlüter
        'secondary' => array(230, 126, 34),  // Orange accent
        'text_dark' => array(44, 62, 80),    // Gris foncé
        'text_light' => array(127, 140, 141), // Gris clair
        'border' => array(189, 195, 199),    // Gris bordure
        'background' => array(236, 240, 241) // Gris fond
    );

    private function convertText($txt)
    {
        // Si le texte est vide, le retourner tel quel
        if (empty($txt)) {
            return $txt;
        }

        // Si le texte est en UTF-8, le convertir
        if (mb_check_encoding($txt, 'UTF-8')) {
            // Utiliser iconv si disponible (plus fiable pour FPDF)
            if (function_exists('iconv')) {
                $converted = iconv('UTF-8', 'ISO-8859-1//IGNORE', $txt);
                if ($converted !== false) {
                    return $converted;
                }
            }

            // Fallback: utiliser notre tableau de correspondance
            $converted = strtr($txt, $this->utf8_to_iso);

            // Si ça ne marche toujours pas, essayer mb_convert_encoding
            if ($converted === $txt) {
                $converted = mb_convert_encoding($txt, 'ISO-8859-1', 'UTF-8');
            }

            return $converted;
        }

        return $txt;
    }

    // Méthodes d'amélioration visuelle
    function setColorFromPalette($colorName)
    {
        if (isset($this->colors[$colorName])) {
            $color = $this->colors[$colorName];
            $this->SetTextColor($color[0], $color[1], $color[2]);
        }
    }

    function setFillColorFromPalette($colorName)
    {
        if (isset($this->colors[$colorName])) {
            $color = $this->colors[$colorName];
            $this->SetFillColor($color[0], $color[1], $color[2]);
        }
    }

    function setDrawColorFromPalette($colorName)
    {
        if (isset($this->colors[$colorName])) {
            $color = $this->colors[$colorName];
            $this->SetDrawColor($color[0], $color[1], $color[2]);
        }
    }

    function addSectionSeparator($y_position = null)
    {
        if ($y_position) $this->SetY($y_position);
        $this->setDrawColorFromPalette('border');
        $this->SetLineWidth(0.5);
        $this->Line(20, $this->GetY(), 190, $this->GetY());
        $this->Ln(8);
    }

    function addSignatureBox($x, $y, $width, $height, $title)
    {
        $this->setDrawColorFromPalette('border');
        $this->SetLineWidth(0.8);
        $this->Rect($x, $y, $width, $height);

        // Titre de la boîte
        $this->SetXY($x, $y - 8);
        $this->SetFont('Arial', 'B', 10);
        $this->setColorFromPalette('text_dark');
        $this->Cell($width, 6, $title, 0, 0, 'C');

        // Ligne pour signature
        $this->SetLineWidth(0.3);
        $this->Line($x + 10, $y + $height - 15, $x + $width - 10, $y + $height - 15);

        // Texte "Signature"
        $this->SetXY($x + 10, $y + $height - 12);
        $this->SetFont('Arial', 'I', 8);
        $this->setColorFromPalette('text_light');
        $this->Cell($width - 20, 4, 'Signature', 0, 0, 'C');
    }

    // Boîte de signature optimisée pour une page
    function addCompactSignatureBox($x, $y, $width, $height, $title)
    {
        $this->setDrawColorFromPalette('border');
        $this->SetLineWidth(0.6);
        $this->Rect($x, $y, $width, $height);

        // Titre compact avec plus d'espacement
        $this->SetXY($x, $y - 8); // Augmenté de -6 à -8mm pour plus d'espace
        $this->SetFont('Arial', 'B', 9);
        $this->setColorFromPalette('text_dark');
        $this->Cell($width, 5, $title, 0, 0, 'C');

        // Ligne pour signature
        $this->SetLineWidth(0.2);
        $this->Line($x + 8, $y + $height - 8, $x + $width - 8, $y + $height - 8);

        // Texte "Signature" compact
        $this->SetXY($x + 8, $y + $height - 6);
        $this->SetFont('Arial', 'I', 7);
        $this->setColorFromPalette('text_light');
        $this->Cell($width - 16, 3, 'Signature', 0, 0, 'C');
    }

    function Cell($w, $h=0, $txt='', $border=0, $ln=0, $align='', $fill=false, $link='')
    {
        $txt = $this->convertText($txt);
        parent::Cell($w, $h, $txt, $border, $ln, $align, $fill, $link);
    }

    function MultiCell($w, $h, $txt, $border=0, $align='J', $fill=false)
    {
        $txt = $this->convertText($txt);
        parent::MultiCell($w, $h, $txt, $border, $align, $fill);
    }

    function Write($h, $txt, $link='')
    {
        $txt = $this->convertText($txt);
        parent::Write($h, $txt, $link);
    }

    // Pied de page amélioré
    function Footer()
    {
        $this->SetY(-20);
        $this->setDrawColorFromPalette('border');
        $this->SetLineWidth(0.3);
        $this->Line(20, $this->GetY(), 190, $this->GetY());

        $this->SetY(-15);
        $this->SetFont('Arial', 'I', 8);
        $this->setColorFromPalette('text_light');

        // Informations de l'entreprise à gauche
        $this->Cell(0, 5, 'Schluter Systems - Service Informatique', 0, 0, 'L');

        // Numéro de page à droite
        $this->Cell(0, 5, 'Page ' . $this->PageNo() . '/{nb}', 0, 0, 'R');
    }
}

// Fonction de connexion à la base de données supprimée
// Fonction améliorée pour ajouter le logo de l'entreprise
function addCompanyLogo($pdf) {
    // Chemins possibles pour le logo
    $logoPaths = [
        'img/schluter-systems-logo-png-transparent.png',
        'C:/laragon/www/projet/img/schluter-systems-logo-png-transparent.png',
        '../img/schluter-systems-logo-png-transparent.png'
    ];

    $logoAdded = false;
    foreach ($logoPaths as $path) {
        if (file_exists($path)) {
            // Positionnement optimisé du logo
            $pdf->Image($path, 150, 15, 35, 0, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
            $logoAdded = true;
            break;
        }
    }

    // Si aucun logo trouvé, ajouter un placeholder textuel élégant
    if (!$logoAdded) {
        $pdf->SetXY(150, 15);
        $pdf->SetFont('Arial', 'B', 12);
        $pdf->setColorFromPalette('primary');
        $pdf->Cell(35, 8, 'SCHLUTER', 0, 1, 'C');
        $pdf->SetX(150);
        $pdf->SetFont('Arial', '', 10);
        $pdf->Cell(35, 6, 'SYSTEMS', 0, 0, 'C');
    }
}

function generatePDF($data) {
    $pdf = new FPDF_UTF8();
    $pdf->SetAutoPageBreak(false); // Contrôle manuel pour optimisation
    $pdf->AliasNbPages();
    $pdf->AddPage();
    $pdf->SetMargins(22, 22, 22);

    // En-tête de la première page
    addOptimizedPDFHeader($pdf, true);

    $isFirstPage = true;
    foreach ($data as $index => $ar) {
        if (!$isFirstPage) {
            $pdf->AddPage();
            addOptimizedPDFHeader($pdf, false);
        }

        generateOptimizedPDFSection($pdf, $ar, $isFirstPage);
        $isFirstPage = false;

        // Vérifier si on a assez d'espace pour le prochain accusé
        if ($index < count($data) - 1) {
            $currentY = $pdf->GetY();
            $pageHeight = 297;
            $bottomMargin = 30;

            // Si moins de 80mm d'espace restant, nouvelle page
            if ($currentY > ($pageHeight - $bottomMargin - 80)) {
                // Nouvelle page pour le prochain accusé
                continue; // Le prochain accusé créera une nouvelle page
            } else {
                // Ajouter une séparation si on reste sur la même page
                $pdf->addSectionSeparator();
                $pdf->Ln(8);
            }
        }
    }

    $filename = 'accuses_reception_' . date('Y-m-d_H-i-s') . '.pdf';
    ob_clean();
    $pdf->Output('F', $filename);
    return $filename;
}

// Fonction pour créer un en-tête professionnel
function addPDFHeader($pdf, $isFirstPage = true) {
    // Logo de l'entreprise
    addCompanyLogo($pdf);

    if ($isFirstPage) {
        // Titre principal avec style amélioré
        $pdf->SetY(25);
        $pdf->SetFont('Arial', 'B', 28);
        $pdf->setColorFromPalette('primary');
        $pdf->Cell(0, 15, 'ACCUSÉ DE RÉCEPTION', 0, 1, 'L');

        // Sous-titre
        $pdf->SetFont('Arial', '', 12);
        $pdf->setColorFromPalette('text_light');
        $pdf->Cell(0, 8, 'Service Informatique - Schluter Systems', 0, 1, 'L');

        // Ligne de séparation élégante
        $pdf->Ln(5);
        $pdf->setDrawColorFromPalette('primary');
        $pdf->SetLineWidth(1.5);
        $pdf->Line(25, $pdf->GetY(), 120, $pdf->GetY());
        $pdf->Ln(15);
    } else {
        // En-tête simplifié pour les pages suivantes
        $pdf->SetY(25);
        $pdf->SetFont('Arial', 'B', 16);
        $pdf->setColorFromPalette('primary');
        $pdf->Cell(0, 10, 'ACCUSÉ DE RÉCEPTION (suite)', 0, 1, 'L');
        $pdf->Ln(10);
    }
}

// Fonction pour créer un en-tête optimisé pour une page
function addOptimizedPDFHeader($pdf, $isFirstPage = true, $ultraCompact = false) {
    // Logo de l'entreprise (position optimisée)
    addCompanyLogo($pdf);

    if ($isFirstPage) {
        if ($ultraCompact) {
            // Version ultra-compacte
            $pdf->SetY(20);
            $pdf->SetFont('Arial', 'B', 20);
            $pdf->setColorFromPalette('primary');
            $pdf->Cell(0, 10, 'ACCUSÉ DE RÉCEPTION', 0, 1, 'L');

            $pdf->SetFont('Arial', '', 9);
            $pdf->setColorFromPalette('text_light');
            $pdf->Cell(0, 5, 'Service Informatique - Schluter Systems', 0, 1, 'L');

            $pdf->Ln(3);
            $pdf->setDrawColorFromPalette('primary');
            $pdf->SetLineWidth(0.8);
            $pdf->Line(18, $pdf->GetY(), 100, $pdf->GetY());
            $pdf->Ln(8);
        } else {
            // Version compacte standard
            $pdf->SetY(22);
            $pdf->SetFont('Arial', 'B', 24);
            $pdf->setColorFromPalette('primary');
            $pdf->Cell(0, 12, 'ACCUSÉ DE RÉCEPTION', 0, 1, 'L');

            $pdf->SetFont('Arial', '', 10);
            $pdf->setColorFromPalette('text_light');
            $pdf->Cell(0, 6, 'Service Informatique - Schlüter Systems', 0, 1, 'L');

            $pdf->Ln(4);
            $pdf->setDrawColorFromPalette('primary');
            $pdf->SetLineWidth(1.0);
            $pdf->Line(22, $pdf->GetY(), 110, $pdf->GetY());
            $pdf->Ln(10);
        }
    } else {
        // En-tête simplifié pour les pages suivantes
        $pdf->SetY(22);
        $pdf->SetFont('Arial', 'B', 14);
        $pdf->setColorFromPalette('primary');
        $pdf->Cell(0, 8, 'ACCUSÉ DE RÉCEPTION (suite)', 0, 1, 'L');
        $pdf->Ln(6);
    }
}

// Fonction pour nettoyer et convertir le texte
function cleanText($text) {
    $text = strip_tags($text);
    if (!mb_check_encoding($text, 'UTF-8')) {
        $text = mb_convert_encoding($text, 'UTF-8', 'auto');
    }
    return $text;
}

// Fonction pour calculer l'espace optimal et ajuster la mise en page
function calculateOptimalLayout($pdf, $contentLength) {
    // Constantes de mise en page pour A4
    $pageHeight = 297; // mm
    $topMargin = 20;   // Réduits de 25 à 20
    $bottomMargin = 30; // Espace pour pied de page
    $headerHeight = 35; // En-tête

    $availableHeight = $pageHeight - $topMargin - $bottomMargin - $headerHeight;

    // Ajustement dynamique selon la longueur du contenu
    if ($contentLength > 500) { // Contenu long
        return [
            'spacing_section' => 8,    // Espacement réduit entre sections
            'spacing_line' => 5,       // Espacement entre lignes
            'info_box_height' => 28,   // Hauteur boîte info augmentée (était 20)
            'signature_height' => 25,  // Hauteur signatures réduite
            'font_size_title' => 11,   // Taille police titres
            'font_size_text' => 9,     // Taille police texte
            'margins' => 20,           // Marges réduites
            'spacing_before_signatures' => 12, // Espacement avant signatures
            'spacing_after_signature_title' => 6 // Espacement après titre "SIGNATURES"
        ];
    } else { // Contenu normal
        return [
            'spacing_section' => 10,
            'spacing_line' => 6,
            'info_box_height' => 30,   // Hauteur boîte info augmentée (était 22)
            'signature_height' => 30,
            'font_size_title' => 12,
            'font_size_text' => 10,
            'margins' => 22,
            'spacing_before_signatures' => 15, // Espacement avant signatures
            'spacing_after_signature_title' => 8 // Espacement après titre "SIGNATURES"
        ];
    }
}

function generateOptimizedPDFSection($pdf, $ar, $isFirstSection = false) {
    // Calculer la longueur du contenu pour optimiser la mise en page
    $contentLength = strlen(cleanText($ar['Remis']) . cleanText($ar['Recu']));
    $layout = calculateOptimalLayout($pdf, $contentLength);

    // Position de départ pour l'encadré
    $startY = $pdf->GetY();

    // Dessiner l'encadré d'abord (position fixe)
    $pdf->setFillColorFromPalette('background');
    $pdf->setDrawColorFromPalette('border');
    $pdf->SetLineWidth(0.3);
    $pdf->Rect(25, $startY, 160, $layout['info_box_height'], 'DF');

    // Contenu de l'encadré avec positionnement précis
    $pdf->SetXY(25, $startY + 5); // Position dans l'encadré avec marge interne ajustée

    // Repositionner pour le contenu (pas de titre)
    $pdf->SetX(25);
    $pdf->Ln(2);

    // Collaborateur et responsable sur la même ligne (optimisé)
    $pdf->SetX(25);
    $pdf->SetFont('Arial', 'B', $layout['font_size_text']);
    $pdf->setColorFromPalette('text_dark');
    $pdf->Cell(35, $layout['spacing_line'], 'Collaborateur :', 0, 0, 'L');
    $pdf->SetFont('Arial', '', $layout['font_size_text']);
    $pdf->Cell(55, $layout['spacing_line'], cleanText($ar['Collaborateur']), 0, 0, 'L');

    $pdf->SetFont('Arial', 'B', $layout['font_size_text']);
    $pdf->Cell(30, $layout['spacing_line'], 'Responsable :', 0, 0, 'L');
    $pdf->SetFont('Arial', '', $layout['font_size_text']);
    // MultiCell pour le responsable, largeur limitée à 35mm
    $xResponsable = $pdf->GetX();
    $yResponsable = $pdf->GetY();
    $pdf->SetXY($xResponsable, $yResponsable);
    $pdf->MultiCell(35, $layout['spacing_line'], cleanText($ar['Responsable']), 0, 'L');
    // S'assurer que la ligne suivante commence à la bonne hauteur
    $pdf->SetY(max($pdf->GetY(), $yResponsable + $layout['spacing_line']));
    $pdf->SetX(25);

    // Date
    $pdf->SetX(25);
    $pdf->SetFont('Arial', 'B', $layout['font_size_text']);
    $pdf->setColorFromPalette('text_dark');
    $pdf->Cell(35, $layout['spacing_line'], 'Date :', 0, 0, 'L');
    $pdf->SetFont('Arial', '', $layout['font_size_text']);
    $pdf->Cell(0, $layout['spacing_line'], date('d/m/Y', strtotime($ar['Date'])), 0, 1, 'L');

    // S'assurer qu'on sort de l'encadré
    $pdf->SetY($startY + $layout['info_box_height'] + $layout['spacing_section']);

    // Section "Matériel remis" optimisée
    $pdf->SetFont('Arial', 'B', $layout['font_size_title']);
    $pdf->setColorFromPalette('secondary');
    $pdf->Cell(0, 6, 'MATÉRIEL REMIS', 0, 1, 'L');

    $pdf->setDrawColorFromPalette('secondary');
    $pdf->SetLineWidth(0.5);
    $pdf->Line(25, $pdf->GetY(), 65, $pdf->GetY());
    $pdf->Ln(4);

    $pdf->SetFont('Arial', '', $layout['font_size_text']);
    $pdf->setColorFromPalette('text_dark');
    $pdf->MultiCell(0, $layout['spacing_line'], cleanText($ar['Remis']), 0, 'L');
    $pdf->Ln($layout['spacing_section']);

    // Section "Matériel reçu" optimisée
    $pdf->SetFont('Arial', 'B', $layout['font_size_title']);
    $pdf->setColorFromPalette('secondary');
    $pdf->Cell(0, 6, 'MATÉRIEL REÇU', 0, 1, 'L');

    $pdf->setDrawColorFromPalette('secondary');
    $pdf->SetLineWidth(0.5);
    $pdf->Line(25, $pdf->GetY(), 60, $pdf->GetY());
    $pdf->Ln(4);

    $pdf->SetFont('Arial', '', $layout['font_size_text']);
    $pdf->setColorFromPalette('text_dark');
    $pdf->MultiCell(0, $layout['spacing_line'], cleanText($ar['Recu']), 0, 'L');

    // Espacement supplémentaire avant les signatures (10-15mm)
    $pdf->Ln($layout['spacing_before_signatures']);

    // Boîtes de signature compactes (sans titre)
    $signatureY = $pdf->GetY();
    $pdf->addCompactSignatureBox(25, $signatureY, 75, $layout['signature_height'], 'Responsable Service Info.');
    $pdf->addCompactSignatureBox(110, $signatureY, 75, $layout['signature_height'], 'Collaborateur');

    $pdf->SetY($signatureY + $layout['signature_height'] + 8);
}

function generateSinglePDF($ar) {
    // Calculer la longueur du contenu pour optimiser les marges
    $contentLength = strlen(cleanText($ar['Remis']) . cleanText($ar['Recu']));
    $layout = calculateOptimalLayout(null, $contentLength);

    $pdf = new FPDF_UTF8();
    $pdf->SetAutoPageBreak(false); // Désactiver pour contrôler manuellement
    $pdf->AliasNbPages();
    $pdf->AddPage();
    $pdf->SetMargins($layout['margins'], $layout['margins'], $layout['margins']);

    // En-tête optimisé pour une page
    addOptimizedPDFHeader($pdf, true);

    // Contenu du document optimisé
    generateOptimizedPDFSection($pdf, $ar, true);

    // Vérifier si on dépasse la page et ajuster si nécessaire
    $currentY = $pdf->GetY();
    $pageHeight = 297; // A4 height in mm
    $bottomMargin = 30;

    if ($currentY > ($pageHeight - $bottomMargin)) {
        // Si on dépasse, régénérer avec des paramètres plus compacts
        $pdf = new FPDF_UTF8();
        $pdf->SetAutoPageBreak(false);
        $pdf->AliasNbPages();
        $pdf->AddPage();
        $pdf->SetMargins(18, 18, 18); // Marges encore plus réduites

        addOptimizedPDFHeader($pdf, true, true); // Mode ultra-compact
        generateOptimizedPDFSection($pdf, $ar, true);
    }

    // Informations supplémentaires compactes en bas
    $remainingSpace = ($pageHeight - $bottomMargin) - $pdf->GetY();
    if ($remainingSpace > 15) {
        $pdf->Ln(5);
        $pdf->setDrawColorFromPalette('border');
        $pdf->SetLineWidth(0.2);
        $pdf->Line($layout['margins'], $pdf->GetY(), 210 - $layout['margins'], $pdf->GetY());
        $pdf->Ln(4);

        $pdf->SetFont('Arial', 'I', 8);
        $pdf->setColorFromPalette('text_light');
        $pdf->Cell(0, 4, 'Document généré le ' . date('d/m/Y à H:i') . ' - Schlüter-Systems', 0, 1, 'C');
    }

    $filename = 'accuse_reception_' . $ar['ID'] . '_' . date('Y-m-d_H-i-s') . '.pdf';
    ob_clean();
    $pdf->Output('D', $filename);
}

// Ajout d'un accusé de réception dans la base
function addAccuseReception($collaborateur, $responsable, $date, $remis, $recu) {
    global $db;
    $sql = "INSERT INTO accuses_reception (collaborateur, responsable, date_creation, materiel_remis, materiel_recu) VALUES (?, ?, ?, ?, ?)";
    $stmt = $db->prepare($sql);
    return $stmt->execute([$collaborateur, $responsable, $date, $remis, $recu]);
}

// Variables pour les messages de feedback
$uploadMessage = '';
$uploadSuccess = false;

if ($_SERVER["REQUEST_METHOD"] == "POST" && !isset($_POST['edit_id'])) {
    $collaborateur = trim($_POST['Collaborateur'] ?? '');
    $responsable = trim($_POST['Responsable'] ?? '');
    $date = trim($_POST['Date'] ?? '');
    $remis = trim($_POST['Remis'] ?? '');
    $recu = trim($_POST['Reçu'] ?? '');

    // Validation des champs obligatoires
    if ($collaborateur === '' || $responsable === '' || $date === '') {
        $uploadMessage = "Merci de remplir tous les champs obligatoires (collaborateur, responsable, date).";
        $uploadSuccess = false;
    } else {
        // Ajouter l'accusé de réception
        $insertResult = addAccuseReception($collaborateur, $responsable, $date, $remis, $recu);
        if ($insertResult) {
            $uploadMessage = "Accusé de réception ajouté avec succès.";
            $uploadSuccess = true;
        } else {
            $uploadMessage = "Erreur lors de l'ajout de l'accusé de réception.";
            $uploadSuccess = false;
        }
    }
}

// Traitement de la modification
if (isset($_POST['edit_id'])) {
    $editId = intval($_POST['edit_id']);
    $collaborateur = trim($_POST['Collaborateur']);
    $responsable = trim($_POST['Responsable']);
    $date = $_POST['Date'];
    $remis = !empty(trim($_POST['Remis'])) ? trim($_POST['Remis']) : null;
    $recu = !empty(trim($_POST['Reçu'])) ? trim($_POST['Reçu']) : null;
    $conn = $db;
    $sql = "UPDATE accuses_reception SET collaborateur = :collaborateur, responsable = :responsable, date_creation = :date, materiel_remis = :remis, materiel_recu = :recu WHERE id = :id";
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        ':collaborateur' => $collaborateur,
        ':responsable' => $responsable,
        ':date' => $date,
        ':remis' => $remis,
        ':recu' => $recu,
        ':id' => $editId
    ]);
    // Redirection pour éviter le repost
    header('Location: accuse_reception.php');
    exit;
}

// Gestion de la suppression (sans message de confirmation)
// if (isset($_GET['delete_id'])) {
//     $deleteId = intval($_GET['delete_id']);
//     if ($deleteId > 0) {
//         $result = deleteAccuseReception($deleteId);
//
//         // Redirection silencieuse après suppression (succès ou échec)
//         header("Location: accuse_reception.php");
//         exit;
//     } else {
//         // Redirection même en cas d'ID invalide
//         header("Location: accuse_reception.php");
//         exit;
//     }
// }

// Gestion de la recherche et de la pagination
$searchTerm = '';
$whereClause = '';
$params = [];

// Pagination
$perPage = 10;
$page = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] > 0 ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $perPage;

if (isset($_GET['search']) && !empty(trim($_GET['search']))) {
    $searchTerm = trim($_GET['search']);
    $whereClause = "WHERE (collaborateur LIKE :search1 OR responsable LIKE :search2)";
    $params = [
        ':search1' => '%' . $searchTerm . '%',
        ':search2' => '%' . $searchTerm . '%'
    ];
}

// Compter le nombre total de résultats pour la pagination
$conn = $db;
$countSql = "SELECT COUNT(*) FROM accuses_reception $whereClause";
$countStmt = $conn->prepare($countSql);
$countStmt->execute($params);
$totalRows = $countStmt->fetchColumn();
$totalPages = max(1, ceil($totalRows / $perPage));

// Récupération des données paginées
$sql = "SELECT id as ID, collaborateur as Collaborateur, responsable as Responsable,
        date_creation as Date, materiel_remis as Remis, materiel_recu as Recu
        FROM accuses_reception
        $whereClause
        ORDER BY date_creation DESC
        LIMIT $perPage OFFSET $offset";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$arData = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (isset($_GET['download_pdf'])) {
    $pdfFile = generatePDF($arData);

    header("Content-Type: application/pdf");
    header("Content-Disposition: attachment; filename=" . basename($pdfFile));
    readfile($pdfFile);
    exit();
}

if (isset($_GET['download_single_pdf'])) {
    $id = intval($_GET['download_single_pdf']);
    $ar = null;
    foreach ($arData as $item) {
        if ($item['ID'] == $id) {
            $ar = $item;
            break;
        }
    }

    if ($ar) {
        $pdfFile = generateSinglePDF($ar);

        header("Content-Disposition: attachment; filename=" . basename($pdfFile));
        header("Content-Type: application/pdf");
        readfile($pdfFile);
        exit;
    }
}

// Gestion du téléchargement groupé PDF
if (isset($_POST['bulk_download']) && !empty($_POST['bulk_ids']) && is_array($_POST['bulk_ids'])) {
    $ids = array_map('intval', $_POST['bulk_ids']);
    if (count($ids) > 0) {
        // Récupérer les accusés sélectionnés
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $sql = "SELECT id as ID, collaborateur as Collaborateur, responsable as Responsable, date_creation as Date, materiel_remis as Recu FROM accuses_reception WHERE id IN ($placeholders) ORDER BY date_creation DESC";
        $stmt = $conn->prepare($sql);
        $stmt->execute($ids);
        $selectedData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        if ($selectedData) {
            $pdfFile = generatePDF($selectedData);
            header("Content-Type: application/pdf");
            header("Content-Disposition: attachment; filename=" . basename($pdfFile));
            readfile($pdfFile);
            exit();
        }
    }
}

// Récupérer l'ID à éditer si présent
$editId = isset($_GET['edit_id']) ? intval($_GET['edit_id']) : null;
$editData = null;
if ($editId) {
    $conn = $db;
    $stmt = $conn->prepare("SELECT id, collaborateur, responsable, date_creation, materiel_remis, materiel_recu FROM accuses_reception WHERE id = :id");
    $stmt->execute([':id' => $editId]);
    $editData = $stmt->fetch();
}



?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accusés de Réception</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        /* Styles pour les messages de feedback */
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            font-weight: 500;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }



        /* Styles pour le formulaire amélioré */
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .required {
            color: #dc3545;
            font-size: 0.9em;
        }
        .optional {
            color: #6c757d;
            font-size: 0.9em;
            font-weight: normal;
        }
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
            resize: vertical;
        }

        .btn-icon {
            margin-right: 5px;
        }

        /* Styles pour le tableau */
        .text-center {
            text-align: center;
        }
        .empty-field {
            color: #6c757d;
            font-style: italic;
            font-size: 0.9em;
        }


        .btn-danger {
            background-color: #dc3545;
            color: white;
            padding: 8px 12px;
            text-decoration: none;
            border-radius: 4px;
            display: inline-block;
            transition: background-color 0.3s;
        }
        .btn-danger:hover {
            background-color: #c82333;
            color: white;
            text-decoration: none;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
            padding: 8px 12px;
            text-decoration: none;
            border-radius: 4px;
            display: inline-block;
            transition: background-color 0.3s;
        }
        .btn-primary:hover {
            background-color: #0056b3;
            color: white;
            text-decoration: none;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
            padding: 8px 12px;
            text-decoration: none;
            border-radius: 4px;
            display: inline-block;
            transition: background-color 0.3s;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
            color: white;
            text-decoration: none;
        }

        /* Styles pour la recherche */
        .search-container {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        .search-form {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        .search-input {
            flex: 1;
            min-width: 220px;
            padding: 10px 15px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .search-btn {
            padding: 10px 20px;
        }
        .reset-btn {
            padding: 10px 20px;
        }
        .search-results {
            margin-top: 10px;
            padding: 8px;
            background: #e8f4fd;
            border-left: 4px solid #005ea2;
            border-radius: 4px;
        }

        /* Styles pour le surlignage des termes de recherche */
        .highlight {
            background-color: #ffeb3b;
            font-weight: bold;
        }

        /* Styles pour la pagination */
        .pagination-wrapper {
            display: flex;
            justify-content: flex-end;
            margin-top: 10px;
            clear: both;
            width: 100%;
            position: static !important;
        }
    </style>
</head>
<body>
<div class="container">
    <h1 class="page-title">Gestion des Accusés de Réception</h1>

    <!-- Messages de feedback -->
    <?php if (!empty($uploadMessage)): ?>
        <div class="alert <?= $uploadSuccess ? 'alert-success' : 'alert-error' ?>">
            <?= htmlspecialchars($uploadMessage, ENT_QUOTES, 'UTF-8') ?>
        </div>
    <?php endif; ?>

    <!-- Form Section -->
    <div class="form-section">
        <div class="form-card">
            <h2><?= $editData ? 'Modifier' : 'Ajouter' ?> un Accusé de Réception</h2>
            <form method="POST" action="">
                <?php if ($editData): ?>
                    <input type="hidden" name="edit_id" value="<?= htmlspecialchars($editData['id'], ENT_QUOTES, 'UTF-8') ?>">
                <?php endif; ?>
                <div class="form-group">
                    <label for="Collaborateur">Collaborateur : <span class="required">*</span></label>
                    <input type="text" id="Collaborateur" name="Collaborateur" required
                           placeholder="Nom du collaborateur"
                           value="<?= $editData ? htmlspecialchars($editData['collaborateur'], ENT_QUOTES, 'UTF-8') : '' ?>">
                </div>
                <div class="form-group">
                    <label for="Responsable">Responsable : <span class="required">*</span></label>
                    <input type="text" id="Responsable" name="Responsable" required
                           placeholder="Nom du responsable"
                           value="<?= $editData ? htmlspecialchars($editData['responsable'], ENT_QUOTES, 'UTF-8') : '' ?>">
                </div>
                <div class="form-group">
                    <label for="Date">Date : <span class="required">*</span></label>
                    <input type="date" id="Date" name="Date" required
                           value="<?= $editData ? htmlspecialchars($editData['date_creation'], ENT_QUOTES, 'UTF-8') : date('Y-m-d') ?>">
                </div>
                <div class="form-group">
                    <label for="Remis">Matériel Remis : <span class="optional">(optionnel)</span></label>
                    <textarea id="Remis" name="Remis" rows="3"
                              placeholder="Décrivez le matériel remis par le collaborateur (ancien équipement, etc.)"><?php if ($editData && $editData['materiel_remis'] !== null) { echo htmlspecialchars($editData['materiel_remis'], ENT_QUOTES, 'UTF-8'); } ?></textarea>
                </div>
                <div class="form-group">
                    <label for="Reçu">Matériel Reçu : <span class="optional">(optionnel)</span></label>
                    <textarea id="Reçu" name="Reçu" rows="3"
                              placeholder="Décrivez le matériel reçu par le collaborateur (ordinateur, téléphone, etc.)"><?php if ($editData && $editData['materiel_recu'] !== null) { echo htmlspecialchars($editData['materiel_recu'], ENT_QUOTES, 'UTF-8'); } ?></textarea>
                </div>
                <button type="submit" class="btn-primary">
                    <span class="btn-icon">✅</span> <?= $editData ? 'Enregistrer les modifications' : 'Ajouter l\'Accusé de Réception' ?>
                </button>
            </form>
        </div>
    </div>

    <!-- Barre de recherche déplacée ici -->
    <h2 class="section-title">Liste des Accusés de Réception</h2>
    <div class="search-container" style="background-color:#f8f9fa;padding:20px;border-radius:8px;margin-bottom:20px;border:1px solid #dee2e6;">
        <form method="GET" action="" class="search-form" id="search-form" style="display:flex;gap:10px;align-items:center;flex-wrap:wrap;">
            <input
                type="text"
                name="search"
                class="search-input"
                placeholder="Rechercher par nom de collaborateur ou responsable..."
                value="<?= htmlspecialchars($searchTerm, ENT_QUOTES, 'UTF-8') ?>"
                autocomplete="off"
                style="flex:1;min-width:220px;padding:10px 15px;border:2px solid #ddd;border-radius:5px;font-size:16px;"
            >
            <button type="submit" class="search-btn btn-primary" style="padding:10px 20px;">🔍 Rechercher</button>
            <?php if (!empty($searchTerm)): ?>
                <a href="accuse_reception.php" class="reset-btn btn-secondary" style="padding:10px 20px;">🔄 Réinitialiser</a>
            <?php endif; ?>
        </form>
        <?php if (isset($_GET['search'])): ?>
            <div class="search-results" style="margin-top:10px;padding:8px;background:#e8f4fd;border-left:4px solid #005ea2;border-radius:4px;">
                <?php if (!empty($searchTerm)): ?>
                    <strong>Résultats pour "<?= htmlspecialchars($searchTerm, ENT_QUOTES, 'UTF-8') ?>" :</strong>
                    <?= count($arData) ?> accusé(s) de réception trouvé(s)
                <?php else: ?>
                    <strong>Affichage de tous les accusés de réception</strong> (<?= count($arData) ?> au total)
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Table Section -->
    
    <div id="table-ajax-container">
        <?php include 'accuse_reception_table.php'; ?>
    </div>
    <!-- Modal de suppression -->
    <div id="delete-modal" style="display:none;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.4);z-index:9999;align-items:center;justify-content:center;">
        <div style="background:#fff;padding:30px 40px;border-radius:8px;max-width:90vw;min-width:300px;text-align:center;box-shadow:0 2px 16px #0002;">
            <h3 style="margin-bottom:20px;">Confirmer la suppression</h3>
            <p id="delete-modal-text"></p>
            <div style="margin-top:20px;">
                <button id="delete-confirm-btn" class="btn-danger">Oui, supprimer</button>
                <button id="delete-cancel-btn" class="btn-secondary" style="margin-left:10px;">Annuler</button>
            </div>
        </div>
    </div>
    <!-- FIN de la modale de suppression -->

<?php include 'bottom_bar.php'; ?>
<div id="alert-success" style="display:none;" class="alert alert-success"></div>
<script>
let deleteId = null;
function loadTable(page, search) {
    let url = 'accuse_reception_table.php?page=' + page;
    if (search) url += '&search=' + encodeURIComponent(search);
    fetch(url)
        .then(r => r.text())
        .then(html => {
            document.getElementById('table-ajax-container').innerHTML = html;
            attachAjaxEvents();
            if (typeof attachAjaxTableEvents === 'function') attachAjaxTableEvents(); // <-- Ajouté pour réattacher les événements PDF
            // Mettre à jour dynamiquement le bloc .search-results
            const resultsBlock = document.querySelector('.search-results');
            if (resultsBlock) {
                // Extraire le nombre de résultats et le terme recherché du tableau (via attributs data ou JS)
                let count = 0;
                const table = document.querySelector('#table-ajax-container table');
                if (table) {
                    // -1 pour l'en-tête
                    count = Math.max(0, table.querySelectorAll('tbody tr').length);
                }
                const searchTerm = document.querySelector('.search-input')?.value || '';
                if (searchTerm) {
                    resultsBlock.innerHTML = `<strong>Résultats pour "${searchTerm.replace(/</g,'&lt;').replace(/>/g,'&gt;')}" :</strong> ${count} accusé(s) de réception trouvé(s)`;
                } else {
                    resultsBlock.innerHTML = `<strong>Affichage de tous les accusés de réception</strong> (${count} au total)`;
                }
            }
        });
}
function attachAjaxEvents() {
    document.querySelectorAll('.ajax-page-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = this.getAttribute('data-page');
            const search = document.querySelector('.search-input')?.value || '';
            loadTable(page, search);
        });
    });
    document.querySelectorAll('.ajax-delete-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            deleteId = this.getAttribute('data-id');
            const collab = this.getAttribute('data-collab');
            document.getElementById('delete-modal-text').innerHTML = `Êtes-vous sûr de vouloir supprimer l'accusé de réception de <b>${collab}</b> ?`;
            document.getElementById('delete-modal').style.display = 'flex';
        });
    });
    document.getElementById('select-all')?.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('input[name="bulk_ids[]"]');
        for (const cb of checkboxes) cb.checked = this.checked;
    });
    document.getElementById('delete-cancel-btn').onclick = function() {
        document.getElementById('delete-modal').style.display = 'none';
        deleteId = null;
    };
    document.getElementById('delete-confirm-btn').onclick = function() {
        if (deleteId) {
            fetch('accuse_reception.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'ajax_delete_id=' + encodeURIComponent(deleteId)
            })
            .then(r => r.json())
            .then(data => {
                document.getElementById('delete-modal').style.display = 'none';
                deleteId = null;
                if (data.success) {
                    document.getElementById('alert-success').innerText = 'Suppression effectuée avec succès.';
                    document.getElementById('alert-success').style.display = 'block';
                    setTimeout(() => { document.getElementById('alert-success').style.display = 'none'; }, 2000);
                    const search = document.querySelector('.search-input')?.value || '';
                    loadTable(1, search);
                } else {
                    alert('Erreur lors de la suppression.');
                }
            });
        }
    };
}

// Remplacer la soumission classique du formulaire par une soumission AJAX
function attachAjaxForm() {
    const form = document.querySelector('.form-section form');
    if (!form) return;
    form.onsubmit = function(e) {
        // Si on est en mode édition (edit_id présent), laisser la soumission normale (pas d'AJAX)
        if (form.querySelector('[name="edit_id"]')) {
            return true;
        }
        e.preventDefault();
        const formData = new FormData(form);
        formData.append('ajax_add_ar', '1');
        fetch('accuse_reception.php', {
            method: 'POST',
            body: new URLSearchParams([...formData])
        })
        .then(r => r.json())
        .then(data => {
            const alert = document.getElementById('alert-success');
            alert.innerText = data.message;
            alert.className = 'alert ' + (data.success ? 'alert-success' : 'alert-error');
            alert.style.display = 'block';
            setTimeout(() => { alert.style.display = 'none'; }, 2000);
            if (data.success) {
                form.reset();
                const search = document.querySelector('.search-input')?.value || '';
                loadTable(1, search);
            }
        });
    };
}
document.addEventListener('DOMContentLoaded', function() {
    attachAjaxEvents();
    attachAjaxForm();
    document.getElementById('search-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const search = document.querySelector('.search-input').value;
        loadTable(1, search);
    });
    // Réinitialisation AJAX de la recherche
    const resetBtn = document.querySelector('.reset-btn');
    if (resetBtn) {
        resetBtn.addEventListener('click', function(e) {
            e.preventDefault();
            document.querySelector('.search-input').value = '';
            loadTable(1, '');
        });
    }
});
</script>
</body>
</html>