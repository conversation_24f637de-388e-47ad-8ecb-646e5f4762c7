<?php
require_once __DIR__ . '/config/database.php';
session_start();
// Contrôle d'accès Compta :
$isCompta = isset($_SESSION['role']) && $_SESSION['role'] === 'Compta';
// Récupérer tous les guides Compta
$guides = [];
try {
    $stmt = $db->query('SELECT * FROM guides_compta ORDER BY created_at DESC');
    $guides = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $guides = [];
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Documentation Comptabilité</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body { font-family: Arial, sans-serif; background: #f7f7fa; margin: 0; }
        .container { max-width: 900px; margin: 40px auto; background: #fff; border-radius: 10px; box-shadow: 0 2px 8px #0001; padding: 32px; }
        h1 { color: #2a3b8f; }
        .add-btn { display: inline-block; background: #2a3b8f; color: #fff; padding: 10px 22px; border-radius: 6px; text-decoration: none; margin-bottom: 24px; }
        .add-btn:hover { background: #1a265f; }
        .guide { background: #f1f3f6; padding: 16px; border-radius: 8px; margin-bottom: 16px; }
        .guide-title { font-weight: bold; cursor: pointer; }
        .guide-content { display: none; margin-top: 10px; }
    </style>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.guide-title').forEach(function(title) {
            title.onclick = function() {
                var content = this.nextElementSibling;
                content.style.display = (content.style.display === 'block') ? 'none' : 'block';
            };
        });
    });
    </script>
</head>
<body>
<div class="container">
    <h1>Documentation Comptabilité</h1>
    <?php if ($isCompta): ?>
    <a href="ajout_guide_compta.php" class="add-btn">+ Ajouter un guide Comptabilité</a>
    <?php endif; ?>
    <div class="guides">
        <?php if (empty($guides)): ?>
            <div style="color:#888;">Aucun guide Comptabilité pour le moment.</div>
        <?php else: ?>
            <?php foreach ($guides as $guide): ?>
            <div class="guide">
                <div class="guide-title"><?= htmlspecialchars($guide['titre']) ?> (<?= htmlspecialchars($guide['categorie']) ?>)</div>
                <div class="guide-content"><?= nl2br(htmlspecialchars($guide['contenu'])) ?></div>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>
</body>
</html>