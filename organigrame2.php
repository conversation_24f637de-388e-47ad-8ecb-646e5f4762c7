<?php
session_start();

// Vérifie si l'utilisateur est connecté
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in']) {
    header('Location: login.php');
    exit();
}

$ldap_host = 'FRVIVM001.schlueter.de';
$ldap_port = 389;
$ldap_domain = 'ISERLOHN';
$ldap_base_dn = 'dc=schlueter,dc=de';
$samaccount = $_SESSION['sAMAccountName'] ?? null;
$password = $_SESSION['user_password'] ?? null;
if (!$samaccount || !$password) {
    echo "Impossible de retrouver le login ou le mot de passe de l'utilisateur. Veuillez vous reconnecter.";
    exit();
}

$ldap_conn = ldap_connect($ldap_host, $ldap_port);
ldap_set_option($ldap_conn, LDAP_OPT_PROTOCOL_VERSION, 3);
ldap_set_option($ldap_conn, LDAP_OPT_REFERRALS, 0);
$ldap_user = $ldap_domain . "\\" . $samaccount;
if (!@ldap_bind($ldap_conn, $ldap_user, $password)) {
    echo "Impossible de se connecter à l'annuaire LDAP avec vos identifiants.";
    exit();
}

// On récupère tous les utilisateurs France (filtre OU=France ou mail se terminant par @schluter.fr)
$attributes = ['displayName', 'title', 'manager', 'distinguishedName', 'mail', 'department', 'company'];
$filter = '(&(objectCategory=person)(objectClass=user)(|(mail=*@schluter.fr)(distinguishedName=*OU=France*))(displayName=*))';
$result = ldap_search($ldap_conn, $ldap_base_dn, $filter, $attributes);
$entries = ldap_get_entries($ldap_conn, $result);

// Construction de la hiérarchie (données prêtes à être utilisées pour un nouvel affichage)
$users = [];
foreach ($entries as $entry) {
    if (!isset($entry['displayname'][0]) || !isset($entry['distinguishedname'][0])) continue;
    $dn = $entry['distinguishedname'][0];
    $users[$dn] = [
        'name' => $entry['displayname'][0],
        'title' => $entry['title'][0] ?? '',
        'manager' => $entry['manager'][0] ?? null,
        'department' => $entry['department'][0] ?? '',
        'company' => $entry['company'][0] ?? '',
        'children' => []
    ];
}
$roots = [];
foreach ($users as $dn => &$user) {
    if ($user['manager'] && isset($users[$user['manager']])) {
        $users[$user['manager']]['children'][] = &$user;
    } else {
        $roots[] = &$user;
    }
}

// Mapping des départements principaux à des sections (adapter selon ton AD)
$sections = [
    'RH' => ['RH', 'Ressources Humaines'],
    'COMMERCIAL' => ['Commercial', 'ADV', 'Marketing'],
    'TECHNIQUE' => ['Technique', 'IT', 'Informatique', 'Support'],
    'LOGISTIQUE' => ['Logistique', 'Supply Chain'],
    'DIRECTION' => ['Direction', 'Directeur'],
    'AUTRES' => []
];
function getSection($department) {
    global $sections;
    foreach ($sections as $section => $keywords) {
        foreach ($keywords as $kw) {
            if (stripos($department, $kw) !== false) return $section;
        }
    }
    return 'AUTRES';
}

// Regrouper les users par section
$sectionUsers = [];
foreach ($roots as $root) {
    $section = getSection($root['department']);
    $sectionUsers[$section][] = $root;
}

ldap_unbind($ldap_conn);
?>
