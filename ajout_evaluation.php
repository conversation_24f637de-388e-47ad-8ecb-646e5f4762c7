<?php
require_once('config/database.php');
// Récupérer les prestataires et interlocuteurs
$prestataires = $db->query("SELECT id, nom FROM prestataires ORDER BY nom")->fetchAll(PDO::FETCH_ASSOC);
$interlocuteurs = $db->query("SELECT id, nom, prenom FROM interlocuteurs ORDER BY nom, prenom")->fetchAll(PDO::FETCH_ASSOC);
$criteres_prestataire = [
    'Communication',
    'Suivi des livraisons',
    'Conformité de la commande',
    'Simplicité de la réception'
];
$criteres_interlocuteur = [
    'Réactivité',
    'Connaissance du produit',
    'Écoute et prise en compte des besoins',
    'Résolution de problèmes',
    'Suivi'
];
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Ajouter une Évaluation</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f8f9fa; margin: 40px; }
        .form-container { background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #ddd; padding: 32px; max-width: 600px; margin: auto; }
        label { display: block; margin-top: 16px; font-weight: bold; }
        select, input, textarea { width: 100%; padding: 8px; margin-top: 4px; border-radius: 4px; border: 1px solid #ccc; }
        button { margin-top: 24px; padding: 10px 24px; background: #005ea2; color: #fff; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #003d6b; }
    </style>
    <script>
    function updateCible() {
        var type = document.getElementById('type').value;
        document.getElementById('prestataire_select').style.display = (type === 'prestataire') ? 'block' : 'none';
        document.getElementById('interlocuteur_select').style.display = (type === 'interlocuteur') ? 'block' : 'none';
        document.getElementById('critere_prestataire').style.display = (type === 'prestataire') ? 'block' : 'none';
        document.getElementById('critere_interlocuteur').style.display = (type === 'interlocuteur') ? 'block' : 'none';
    }
    </script>
</head>
<body onload="updateCible()">
    <?php include 'top_bar_eval_IT.php'; ?>
    <div style="margin-bottom:24px;">
        <a href="evaluation_system.php" style="display:inline-block;padding:10px 22px;background:#005ea2;color:#fff;border-radius:5px;text-decoration:none;font-weight:bold;">&larr; Retour à l'accueil</a>
    </div>
    <div class="form-container">
        <h1>Ajouter une Évaluation</h1>
        <form method="post" action="">
            <label for="type">Type d'évaluation *</label>
            <select id="type" name="type" onchange="updateCible()" required>
                <option value="prestataire">Prestataire</option>
                <option value="interlocuteur">Interlocuteur</option>
            </select>

            <div id="prestataire_select">
                <label for="cible_prestataire">Prestataire *</label>
                <select id="cible_prestataire" name="cible_prestataire">
                    <option value="">Sélectionner...</option>
                    <?php foreach ($prestataires as $p) {
                        echo '<option value="'.$p['id'].'">'.htmlspecialchars($p['nom']).'</option>';
                    } ?>
                </select>
            </div>
            <div id="interlocuteur_select" style="display:none;">
                <label for="cible_interlocuteur">Interlocuteur *</label>
                <select id="cible_interlocuteur" name="cible_interlocuteur">
                    <option value="">Sélectionner...</option>
                    <?php foreach ($interlocuteurs as $i) {
                        echo '<option value="'.$i['id'].'">'.htmlspecialchars($i['nom'].' '.$i['prenom']).'</option>';
                    } ?>
                </select>
            </div>

            <label for="date_evaluation">Date *</label>
            <input type="date" id="date_evaluation" name="date_evaluation" required value="<?php echo date('Y-m-d'); ?>">

            <div id="critere_prestataire">
                <label for="critere_prestataire">Critère *</label>
                <select name="critere_prestataire">
                    <?php foreach ($criteres_prestataire as $c) {
                        echo '<option value="'.htmlspecialchars($c).'">'.htmlspecialchars($c).'</option>';
                    } ?>
                </select>
            </div>
            <div id="critere_interlocuteur" style="display:none;">
                <label for="critere_interlocuteur">Critère *</label>
                <select name="critere_interlocuteur">
                    <?php foreach ($criteres_interlocuteur as $c) {
                        echo '<option value="'.htmlspecialchars($c).'">'.htmlspecialchars($c).'</option>';
                    } ?>
                </select>
            </div>

            <label for="note">Note (1 à 5) *</label>
            <select id="note" name="note" required>
                <?php for ($i=1; $i<=5; $i++) echo "<option value='$i'>$i</option>"; ?>
            </select>

            <label for="commentaire">Commentaire</label>
            <textarea id="commentaire" name="commentaire"></textarea>

            <button type="submit">Ajouter</button>
        </form>
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $type = $_POST['type'];
            $cible_id = ($type === 'prestataire') ? $_POST['cible_prestataire'] : $_POST['cible_interlocuteur'];
            $date_evaluation = $_POST['date_evaluation'];
            $critere = ($type === 'prestataire') ? $_POST['critere_prestataire'] : $_POST['critere_interlocuteur'];
            $note = $_POST['note'];
            $commentaire = $_POST['commentaire'];
            if ($cible_id) {
                $sql = "INSERT INTO evaluations (type, cible_id, date_evaluation, critere, note, commentaire) VALUES (?, ?, ?, ?, ?, ?)";
                $stmt = $db->prepare($sql);
                $stmt->execute([$type, $cible_id, $date_evaluation, $critere, $note, $commentaire]);
                echo '<p style="color:green;">Évaluation ajoutée avec succès !</p>';
            }
        }
        ?>
    </div>
</body>
</html>
