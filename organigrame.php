<?php
session_start();

// Vérifie si l'utilisateur est connecté
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in']) {
    header('Location: login.php');
    exit();
}

$ldap_host = 'FRVIVM001.schlueter.de';
$ldap_port = 389;
$ldap_domain = 'ISERLOHN';
$ldap_base_dn = 'dc=schlueter,dc=de';
$samaccount = $_SESSION['sAMAccountName'] ?? null;
$password = $_SESSION['user_password'] ?? null;
if (!$samaccount || !$password) {
    echo "Impossible de retrouver le login ou le mot de passe de l'utilisateur. Veuillez vous reconnecter.";
    exit();
}

$ldap_conn = ldap_connect($ldap_host, $ldap_port);
ldap_set_option($ldap_conn, LDAP_OPT_PROTOCOL_VERSION, 3);
ldap_set_option($ldap_conn, LDAP_OPT_REFERRALS, 0);
$ldap_user = $ldap_domain . "\\" . $samaccount;
if (!@ldap_bind($ldap_conn, $ldap_user, $password)) {
    echo "Impossible de se connecter à l'annuaire LDAP avec vos identifiants.";
    exit();
}

// On récupère tous les utilisateurs France (filtre OU=France ou mail se terminant par @schluter.fr)
$attributes = ['displayName', 'title', 'manager', 'distinguishedName', 'mail', 'department', 'company'];
$filter = '(&(objectCategory=person)(objectClass=user)(|(mail=*@schluter.fr)(distinguishedName=*OU=France*))(displayName=*))';
$result = ldap_search($ldap_conn, $ldap_base_dn, $filter, $attributes);
$entries = ldap_get_entries($ldap_conn, $result);

// Construction de la hiérarchie (données prêtes à être utilisées pour un nouvel affichage)
$users = [];
foreach ($entries as $entry) {
    if (!isset($entry['displayname'][0]) || !isset($entry['distinguishedname'][0])) continue;
    $dn = $entry['distinguishedname'][0];
    $users[$dn] = [
        'name' => $entry['displayname'][0],
        'title' => $entry['title'][0] ?? '',
        'manager' => $entry['manager'][0] ?? null,
        'department' => $entry['department'][0] ?? '',
        'company' => $entry['company'][0] ?? '',
        'children' => []
    ];
}
$roots = [];
foreach ($users as $dn => &$user) {
    if ($user['manager'] && isset($users[$user['manager']])) {
        $users[$user['manager']]['children'][] = &$user;
    } else {
        $roots[] = &$user;
    }
}
// A ce stade, aucune sortie HTML, tout l'affichage a été supprimé.
// Génération de l'organigramme interactif
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Organigramme Schlüter Systems</title>
    <link rel="stylesheet" href="css/organigramme-modulaire.css">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="js/organigramme-modulaire.js"></script>
</head>
<body>

    <div class="org-filters">
        <label>Filtrer par département :
            <select id="org-filter-dept">
                <option value="">Tous</option>
                <?php
                $depts = array_unique(array_map(fn($u) => $u['department'], $users));
                foreach ($depts as $dept) {
                    if ($dept) echo '<option value="'.htmlspecialchars($dept).'">'.htmlspecialchars($dept).'</option>';
                }
                ?>
            </select>
        </label>
    </div>
    <div id="org-breadcrumb"></div>
    <div id="org-chart">
        <?php
        function renderOrgNode($user, $level = 0) {
            $deptClass = strtolower(preg_replace('/[^a-z]/i', '', $user['department'] ?: 'autres'));
            echo '<div class="org-node dept-'.$deptClass.'" data-dn="'.htmlspecialchars($user['name']).'" data-dept="'.htmlspecialchars($user['department']).'">';
            echo '<div class="org-person" tabindex="0" data-name="'.htmlspecialchars($user['name']).'" data-title="'.htmlspecialchars($user['title']).'" data-mail="'.htmlspecialchars($user['mail'] ?? '').'" data-dept="'.htmlspecialchars($user['department']).'" data-company="'.htmlspecialchars($user['company']).'">';
            echo '<span class="org-name">'.htmlspecialchars($user['name']).'</span>';
            if ($user['title']) echo '<span class="org-title">'.htmlspecialchars($user['title']).'</span>';
            echo '</div>';
            if (!empty($user['children'])) {
                echo '<div class="org-children">';
                foreach ($user['children'] as $child) {
                    renderOrgNode($child, $level+1);
                }
                echo '</div>';
            }
            echo '</div>';
        }
        foreach ($roots as $root) {
            renderOrgNode($root);
        }
        ?>
    </div>
</div>
<!-- Fiche modale -->
<div id="org-modal" class="org-modal" style="display:none;">
    <div class="org-modal-content">
        <span class="org-modal-close">&times;</span>
        <div id="org-modal-body"></div>
    </div>
</div>
</body>
</html>
<?php
// Fermeture de la connexion LDAP
ldap_unbind($ldap_conn);
?>
